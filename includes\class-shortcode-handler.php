<?php
/**
 * Shortcode Handler Class
 * Handles shortcode registration and rendering
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DakoiiShortcodeHandler {
    
    /**
     * Map manager instance
     */
    private $map_manager;
    
    /**
     * Boundary loader instance
     */
    private $boundary_loader;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->map_manager = new DakoiiMapManager();
        $this->boundary_loader = new DakoniiBoundaryLoader();
        
        // Register shortcode
        add_shortcode('dakoii_map', array($this, 'render_map_shortcode'));
        
        // Add AJAX handlers for frontend
        add_action('wp_ajax_dakoii_get_map_data', array($this, 'ajax_get_map_data'));
        add_action('wp_ajax_nopriv_dakoii_get_map_data', array($this, 'ajax_get_map_data'));
    }
    
    /**
     * Render map shortcode
     * 
     * @param array $atts Shortcode attributes
     * @param string $content Shortcode content
     * @return string Rendered HTML
     */
    public function render_map_shortcode($atts, $content = '') {
        // Parse attributes
        $atts = shortcode_atts(array(
            'id' => '',
            'shortcode' => '',
            'width' => '',
            'height' => '',
            'zoom' => '',
            'center_lat' => '',
            'center_lng' => '',
            'class' => ''
        ), $atts, 'dakoii_map');
        
        // Get map data
        $map = $this->get_map_from_attributes($atts);
        
        if (!$map) {
            return $this->render_error_message(__('Map not found', 'dakoii-maps'));
        }
        
        // Override config with shortcode attributes
        $config = $this->override_config_with_attributes($map->config, $atts);
        
        // Generate unique container ID
        $container_id = 'dakoii-map-' . $map->id . '-' . uniqid();
        
        // Enqueue required assets
        $this->enqueue_map_assets();
        
        // Render map HTML
        return $this->render_map_html($container_id, $map, $config, $atts);
    }
    
    /**
     * Get map from shortcode attributes
     * 
     * @param array $atts Shortcode attributes
     * @return object|null Map object or null
     */
    private function get_map_from_attributes($atts) {
        // Try to get map by ID first
        if (!empty($atts['id'])) {
            return $this->map_manager->get_map(intval($atts['id']));
        }
        
        // Try to get map by shortcode
        if (!empty($atts['shortcode'])) {
            return $this->map_manager->get_map_by_shortcode($atts['shortcode']);
        }
        
        // If no ID or shortcode provided, try to extract from shortcode tag
        $shortcode_tag = $this->extract_shortcode_from_tag();
        if ($shortcode_tag) {
            return $this->map_manager->get_map_by_shortcode($shortcode_tag);
        }
        
        return null;
    }
    
    /**
     * Extract shortcode from the actual shortcode tag
     * This is a fallback method for auto-generated shortcodes
     * 
     * @return string|null Shortcode or null
     */
    private function extract_shortcode_from_tag() {
        // Get current shortcode tag from WordPress globals
        global $shortcode_tags;
        
        // This is a simplified approach - in practice, you might need
        // to parse the current shortcode being processed
        return null;
    }
    
    /**
     * Override map config with shortcode attributes
     * 
     * @param array $config Original map config
     * @param array $atts Shortcode attributes
     * @return array Modified config
     */
    private function override_config_with_attributes($config, $atts) {
        // Override display options
        if (!empty($atts['width'])) {
            $config['display_options']['width'] = sanitize_text_field($atts['width']);
        }
        
        if (!empty($atts['height'])) {
            $config['display_options']['height'] = sanitize_text_field($atts['height']);
        }
        
        if (!empty($atts['zoom']) && is_numeric($atts['zoom'])) {
            $config['display_options']['zoom']['initial'] = intval($atts['zoom']);
        }
        
        if (!empty($atts['center_lat']) && is_numeric($atts['center_lat'])) {
            $config['display_options']['center']['lat'] = floatval($atts['center_lat']);
        }
        
        if (!empty($atts['center_lng']) && is_numeric($atts['center_lng'])) {
            $config['display_options']['center']['lng'] = floatval($atts['center_lng']);
        }
        
        return $config;
    }
    
    /**
     * Enqueue map assets
     */
    private function enqueue_map_assets() {
        // Leaflet CSS
        if (!wp_style_is('leaflet', 'enqueued')) {
            wp_enqueue_style(
                'leaflet',
                DAKOII_MAPS_PLUGIN_URL . 'assets/js/leaflet/leaflet.css',
                array(),
                '1.9.4'
            );
        }
        
        // Frontend CSS
        if (!wp_style_is('dakoii-maps-frontend', 'enqueued')) {
            wp_enqueue_style(
                'dakoii-maps-frontend',
                DAKOII_MAPS_PLUGIN_URL . 'assets/css/frontend-style.css',
                array('leaflet'),
                DAKOII_MAPS_VERSION
            );
        }
        
        // Leaflet JS
        if (!wp_script_is('leaflet', 'enqueued')) {
            wp_enqueue_script(
                'leaflet',
                DAKOII_MAPS_PLUGIN_URL . 'assets/js/leaflet/leaflet.js',
                array(),
                '1.9.4',
                true
            );
        }
        
        // Frontend JS
        if (!wp_script_is('dakoii-maps-frontend', 'enqueued')) {
            wp_enqueue_script(
                'dakoii-maps-frontend',
                DAKOII_MAPS_PLUGIN_URL . 'assets/js/frontend-script.js',
                array('jquery', 'leaflet'),
                DAKOII_MAPS_VERSION,
                true
            );
            
            // Localize script
            wp_localize_script('dakoii-maps-frontend', 'dakoiiMaps', array(
                'ajaxUrl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dakoii_maps_nonce'),
                'pluginUrl' => DAKOII_MAPS_PLUGIN_URL,
                'strings' => array(
                    'loading' => __('Loading map...', 'dakoii-maps'),
                    'error' => __('Error loading map', 'dakoii-maps'),
                    'noData' => __('No map data available', 'dakoii-maps')
                )
            ));
        }
    }
    
    /**
     * Render map HTML
     * 
     * @param string $container_id Container ID
     * @param object $map Map object
     * @param array $config Map configuration
     * @param array $atts Shortcode attributes
     * @return string HTML output
     */
    private function render_map_html($container_id, $map, $config, $atts) {
        $width = $config['display_options']['width'];
        $height = $config['display_options']['height'];
        $class = !empty($atts['class']) ? ' ' . sanitize_html_class($atts['class']) : '';
        
        // Build container style
        $style_parts = array();
        $style_parts[] = "width: {$width}";
        $style_parts[] = "height: {$height}";
        $style = implode('; ', $style_parts);
        
        // Prepare map data for JavaScript
        $map_data = array(
            'map_id' => $map->id,
            'config' => $config,
            'container_id' => $container_id
        );
        
        ob_start();
        ?>
        <div class="dakoii-map-wrapper<?php echo esc_attr($class); ?>">
            <div 
                id="<?php echo esc_attr($container_id); ?>" 
                class="dakoii-map-container" 
                style="<?php echo esc_attr($style); ?>"
                data-map-config="<?php echo esc_attr(wp_json_encode($map_data)); ?>"
            >
                <div class="dakoii-map-loading">
                    <div class="dakoii-loading-spinner"></div>
                    <p><?php _e('Loading map...', 'dakoii-maps'); ?></p>
                </div>
            </div>
            
            <?php if ($config['features']['legend_enabled']): ?>
            <div class="dakoii-map-legend">
                <h4><?php _e('Legend', 'dakoii-maps'); ?></h4>
                <!-- Legend content will be populated by JavaScript -->
            </div>
            <?php endif; ?>
            
            <?php if ($config['features']['click_navigation']): ?>
            <div class="dakoii-map-breadcrumb" style="display: none;">
                <span class="breadcrumb-label"><?php _e('Location:', 'dakoii-maps'); ?></span>
                <span class="breadcrumb-path"></span>
                <button type="button" class="breadcrumb-reset"><?php _e('Reset View', 'dakoii-maps'); ?></button>
            </div>
            <?php endif; ?>
        </div>
        
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            if (typeof DakoiiMapDisplay !== 'undefined') {
                new DakoiiMapDisplay('<?php echo esc_js($container_id); ?>', <?php echo wp_json_encode($map_data); ?>);
            } else {
                console.error('DakoiiMapDisplay class not found. Make sure frontend script is loaded.');
            }
        });
        </script>
        <?php
        
        return ob_get_clean();
    }
    
    /**
     * Render error message
     * 
     * @param string $message Error message
     * @return string HTML error output
     */
    private function render_error_message($message) {
        return sprintf(
            '<div class="dakoii-map-error"><p>%s</p></div>',
            esc_html($message)
        );
    }
    
    /**
     * AJAX: Get map data for frontend
     */
    public function ajax_get_map_data() {
        check_ajax_referer('dakoii_maps_nonce', 'nonce');
        
        $map_id = intval($_POST['map_id'] ?? 0);
        
        if (!$map_id) {
            wp_send_json_error(__('Map ID is required', 'dakoii-maps'));
        }
        
        // Get map
        $map = $this->map_manager->get_map($map_id);
        
        if (!$map) {
            wp_send_json_error(__('Map not found', 'dakoii-maps'));
        }
        
        // Get boundary data based on map level and boundaries
        $boundary_data = $this->get_map_boundary_data($map);
        
        if (empty($boundary_data)) {
            wp_send_json_error(__('No boundary data available', 'dakoii-maps'));
        }
        
        wp_send_json_success(array(
            'map' => $map,
            'boundaries' => $boundary_data
        ));
    }
    
    /**
     * Get boundary data for a map
     * 
     * @param object $map Map object
     * @return array Boundary data
     */
    private function get_map_boundary_data($map) {
        $filters = array();
        
        // Build filters based on map boundaries
        if (!empty($map->boundaries)) {
            $geocode_prefixes = array();
            $ids = array();
            
            foreach ($map->boundaries as $boundary) {
                if (!empty($boundary->geocode)) {
                    $geocode_prefixes[] = $boundary->geocode;
                }
                if (!empty($boundary->boundary_id)) {
                    $ids[] = $boundary->boundary_id;
                }
            }
            
            if (!empty($geocode_prefixes)) {
                $filters['geocode_prefix'] = $geocode_prefixes;
            }
            
            if (!empty($ids)) {
                $filters['ids'] = $ids;
            }
        }
        
        // Load boundaries based on map level
        return $this->boundary_loader->load_boundaries($map->map_level, $filters);
    }
    
    /**
     * Register dynamic shortcodes for existing maps
     * This method can be called to register shortcodes for all existing maps
     */
    public function register_dynamic_shortcodes() {
        $maps = $this->map_manager->get_all_maps(array('status' => 'active'));
        
        foreach ($maps as $map) {
            if (!empty($map->shortcode)) {
                add_shortcode($map->shortcode, array($this, 'render_dynamic_shortcode'));
            }
        }
    }
    
    /**
     * Render dynamic shortcode
     * This handles shortcodes that are generated dynamically
     * 
     * @param array $atts Shortcode attributes
     * @param string $content Shortcode content
     * @param string $tag Shortcode tag
     * @return string Rendered HTML
     */
    public function render_dynamic_shortcode($atts, $content = '', $tag = '') {
        // Extract map shortcode from tag
        $map = $this->map_manager->get_map_by_shortcode($tag);
        
        if (!$map) {
            return $this->render_error_message(__('Map not found', 'dakoii-maps'));
        }
        
        // Add shortcode to attributes for processing
        $atts['shortcode'] = $tag;
        
        return $this->render_map_shortcode($atts, $content);
    }
}
