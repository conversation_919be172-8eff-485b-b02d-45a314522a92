/**
 * Dako<PERSON> Maps Frontend Styles
 */

/* Map Container */
.dakoii-map-wrapper {
    position: relative;
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dakoii-map-container {
    position: relative;
    width: 100%;
    height: 500px;
    background: #f8f9fa;
}

/* Loading State */
.dakoii-map-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    z-index: 1000;
}

.dakoii-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: dakoii-spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes dakoii-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dakoii-map-loading p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Error State */
.dakoii-map-error {
    padding: 20px;
    text-align: center;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    border-radius: 4px;
    margin: 20px 0;
}

.dakoii-map-error p {
    margin: 0;
    font-size: 14px;
}

/* Map Controls */
.dakoii-map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.dakoii-map-control {
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 3px;
    padding: 5px 8px;
    cursor: pointer;
    font-size: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    transition: all 0.2s;
}

.dakoii-map-control:hover {
    background: #f0f0f0;
    border-color: #999;
}

.dakoii-map-control.active {
    background: #0073aa;
    color: #fff;
    border-color: #0073aa;
}

/* Breadcrumb Navigation */
.dakoii-map-breadcrumb {
    background: #f8f9fa;
    border-top: 1px solid #ddd;
    padding: 10px 15px;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.breadcrumb-label {
    font-weight: 600;
    color: #333;
}

.breadcrumb-path {
    flex: 1;
    color: #666;
}

.breadcrumb-path a {
    color: #0073aa;
    text-decoration: none;
}

.breadcrumb-path a:hover {
    text-decoration: underline;
}

.breadcrumb-separator {
    margin: 0 5px;
    color: #999;
}

.breadcrumb-reset {
    background: #0073aa;
    color: #fff;
    border: none;
    border-radius: 3px;
    padding: 4px 8px;
    font-size: 11px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.breadcrumb-reset:hover {
    background: #005a87;
}

/* Map Legend */
.dakoii-map-legend {
    background: #fff;
    border-top: 1px solid #ddd;
    padding: 15px;
}

.dakoii-map-legend h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
}

.legend-color {
    width: 16px;
    height: 16px;
    border: 1px solid #ccc;
    border-radius: 2px;
}

/* Map Popup Styles */
.dakoii-popup {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    max-width: 250px;
}

.dakoii-popup h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.dakoii-popup p {
    margin: 5px 0;
    font-size: 13px;
    color: #666;
}

.dakoii-popup strong {
    color: #333;
}

.dakoii-popup .popup-actions {
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #eee;
}

.dakoii-popup .popup-action {
    display: inline-block;
    padding: 3px 8px;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    border-radius: 2px;
    font-size: 11px;
    margin-right: 5px;
    transition: background-color 0.2s;
}

.dakoii-popup .popup-action:hover {
    background: #005a87;
    color: #fff;
}

/* Leaflet Overrides */
.dakoii-map-container .leaflet-container {
    font-family: inherit;
}

.dakoii-map-container .leaflet-popup-content-wrapper {
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.dakoii-map-container .leaflet-popup-tip {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.dakoii-map-container .leaflet-control-zoom {
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.dakoii-map-container .leaflet-control-zoom a {
    border-bottom: 1px solid #ccc;
    color: #333;
    font-weight: bold;
}

.dakoii-map-container .leaflet-control-zoom a:last-child {
    border-bottom: none;
}

.dakoii-map-container .leaflet-control-zoom a:hover {
    background: #f0f0f0;
    color: #333;
}

/* Search Control */
.dakoii-search-control {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 1000;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    padding: 5px;
}

.dakoii-search-input {
    border: none;
    outline: none;
    padding: 5px 8px;
    font-size: 13px;
    width: 200px;
}

.dakoii-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ccc;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    display: none;
}

.dakoii-search-result {
    padding: 8px 10px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    font-size: 12px;
}

.dakoii-search-result:last-child {
    border-bottom: none;
}

.dakoii-search-result:hover {
    background: #f0f0f0;
}

.dakoii-search-result.selected {
    background: #e3f2fd;
    color: #1976d2;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dakoii-map-wrapper {
        margin: 10px 0;
        border-radius: 4px;
    }
    
    .dakoii-map-container {
        height: 300px;
    }
    
    .dakoii-map-breadcrumb {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .breadcrumb-path {
        word-break: break-all;
    }
    
    .legend-items {
        flex-direction: column;
        gap: 8px;
    }
    
    .dakoii-search-control {
        position: relative;
        top: auto;
        left: auto;
        margin-bottom: 10px;
        width: 100%;
        box-sizing: border-box;
    }
    
    .dakoii-search-input {
        width: 100%;
        box-sizing: border-box;
    }
    
    .dakoii-map-controls {
        top: 5px;
        right: 5px;
    }
    
    .dakoii-map-control {
        padding: 3px 6px;
        font-size: 11px;
    }
}

@media (max-width: 480px) {
    .dakoii-map-container {
        height: 250px;
    }
    
    .dakoii-map-legend {
        padding: 10px;
    }
    
    .dakoii-map-breadcrumb {
        padding: 8px 10px;
        font-size: 12px;
    }
    
    .dakoii-popup {
        max-width: 200px;
    }
    
    .dakoii-popup h4 {
        font-size: 14px;
    }
    
    .dakoii-popup p {
        font-size: 12px;
    }
}

/* Print Styles */
@media print {
    .dakoii-map-controls,
    .dakoii-search-control,
    .breadcrumb-reset {
        display: none !important;
    }
    
    .dakoii-map-wrapper {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .dakoii-map-container {
        height: 400px !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .dakoii-map-wrapper {
        border-color: #000;
    }
    
    .dakoii-map-control {
        border-color: #000;
        background: #fff;
        color: #000;
    }
    
    .dakoii-map-control:hover {
        background: #000;
        color: #fff;
    }
    
    .dakoii-popup h4 {
        border-bottom-color: #000;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .dakoii-loading-spinner {
        animation: none;
    }
    
    .dakoii-map-control,
    .breadcrumb-reset,
    .dakoii-popup .popup-action {
        transition: none;
    }
}
