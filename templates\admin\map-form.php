<?php
/**
 * Admin template: Map Form (Add/Edit)
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$is_edit = ($mode === 'edit' && $map);
$page_title = $is_edit ? __('Edit Map', 'dakoii-maps') : __('Add New Map', 'dakoii-maps');
$form_action = $is_edit ? admin_url('admin.php?page=dakoii-maps-edit&map_id=' . $map->id) : admin_url('admin.php?page=dakoii-maps-add');

// Get current values
$map_name = $is_edit ? $map->name : '';
$map_description = $is_edit ? $map->description : '';
$map_level = $is_edit ? $map->map_level : 'provincial';
$config = $is_edit ? $map->config : array();

// Set default config values
$default_config = array(
    'display_options' => array(
        'width' => '100%',
        'height' => '500px',
        'zoom' => array('initial' => 6, 'min' => 4, 'max' => 12),
        'center' => array('lat' => -6.314993, 'lng' => 143.95555)
    ),
    'styling' => array(
        'boundary_color' => '#3388ff',
        'boundary_width' => 2,
        'fill_color' => '#3388ff',
        'fill_opacity' => 0.2,
        'hover_color' => '#ff7800',
        'hover_opacity' => 0.7
    ),
    'features' => array(
        'popup_enabled' => true,
        'click_navigation' => true,
        'search_enabled' => false,
        'legend_enabled' => false
    )
);

$config = wp_parse_args($config, $default_config);
?>

<div class="wrap dakoii-maps-admin">
    <div class="dakoii-maps-header">
        <h1><?php echo esc_html($page_title); ?></h1>
        <p><?php _e('Configure your interactive Papua New Guinea administrative boundary map.', 'dakoii-maps'); ?></p>
    </div>

    <form method="post" action="<?php echo esc_url($form_action); ?>" id="dakoii-map-form" class="dakoii-map-form">
        <?php wp_nonce_field('dakoii_save_map', 'dakoii_map_nonce'); ?>
        
        <?php if ($is_edit): ?>
            <input type="hidden" name="map_id" value="<?php echo esc_attr($map->id); ?>">
        <?php endif; ?>
        
        <input type="hidden" name="dakoii_save_map" value="1">

        <!-- Basic Information Section -->
        <div class="dakoii-form-section">
            <h3><?php _e('Basic Information', 'dakoii-maps'); ?></h3>
            <p class="description"><?php _e('Enter the basic details for your map.', 'dakoii-maps'); ?></p>

            <div class="dakoii-form-row">
                <label for="map_name"><?php _e('Map Name', 'dakoii-maps'); ?> <span class="required">*</span></label>
                <input type="text" id="map_name" name="map_name" value="<?php echo esc_attr($map_name); ?>" 
                       placeholder="<?php _e('Enter map name...', 'dakoii-maps'); ?>" required>
                <p class="form-help"><?php _e('A descriptive name for your map (e.g., "Western Province Districts").', 'dakoii-maps'); ?></p>
            </div>

            <div class="dakoii-form-row">
                <label for="map_description"><?php _e('Description', 'dakoii-maps'); ?></label>
                <textarea id="map_description" name="map_description" 
                          placeholder="<?php _e('Optional description...', 'dakoii-maps'); ?>"><?php echo esc_textarea($map_description); ?></textarea>
                <p class="form-help"><?php _e('Optional description for administrative purposes (not displayed on frontend).', 'dakoii-maps'); ?></p>
            </div>
        </div>

        <!-- Map Level Section -->
        <div class="dakoii-form-section">
            <h3><?php _e('Map Level', 'dakoii-maps'); ?></h3>
            <p class="description"><?php _e('Choose the administrative level for your map.', 'dakoii-maps'); ?></p>

            <div class="map-level-options">
                <div class="map-level-option <?php echo ($map_level === 'provincial') ? 'selected' : ''; ?>">
                    <input type="radio" id="level_provincial" name="map_level" value="provincial" 
                           <?php checked($map_level, 'provincial'); ?>>
                    <label for="level_provincial">
                        <span class="level-icon">🗺️</span>
                        <div class="level-title"><?php _e('Provincial', 'dakoii-maps'); ?></div>
                        <div class="level-description"><?php _e('22 provinces with drill-down capability', 'dakoii-maps'); ?></div>
                    </label>
                </div>

                <div class="map-level-option <?php echo ($map_level === 'district') ? 'selected' : ''; ?>">
                    <input type="radio" id="level_district" name="map_level" value="district" 
                           <?php checked($map_level, 'district'); ?>>
                    <label for="level_district">
                        <span class="level-icon">📍</span>
                        <div class="level-title"><?php _e('District', 'dakoii-maps'); ?></div>
                        <div class="level-description"><?php _e('87 districts with LLG drill-down', 'dakoii-maps'); ?></div>
                    </label>
                </div>

                <div class="map-level-option <?php echo ($map_level === 'llg') ? 'selected' : ''; ?>">
                    <input type="radio" id="level_llg" name="map_level" value="llg" 
                           <?php checked($map_level, 'llg'); ?>>
                    <label for="level_llg">
                        <span class="level-icon">📌</span>
                        <div class="level-title"><?php _e('LLG', 'dakoii-maps'); ?></div>
                        <div class="level-description"><?php _e('326 Local Level Governments (most detailed)', 'dakoii-maps'); ?></div>
                    </label>
                </div>
            </div>

            <div class="level-help" data-level="provincial" style="<?php echo ($map_level !== 'provincial') ? 'display: none;' : ''; ?>">
                <p><?php _e('Provincial maps show all 22 provinces. Users can click on provinces to drill down to districts.', 'dakoii-maps'); ?></p>
            </div>
            <div class="level-help" data-level="district" style="<?php echo ($map_level !== 'district') ? 'display: none;' : ''; ?>">
                <p><?php _e('District maps show districts within selected provinces. Users can click on districts to drill down to LLGs.', 'dakoii-maps'); ?></p>
            </div>
            <div class="level-help" data-level="llg" style="<?php echo ($map_level !== 'llg') ? 'display: none;' : ''; ?>">
                <p><?php _e('LLG maps show Local Level Governments within selected districts. This is the most detailed level.', 'dakoii-maps'); ?></p>
            </div>
        </div>

        <!-- Boundary Selection Section -->
        <div class="dakoii-form-section">
            <h3><?php _e('Boundary Selection', 'dakoii-maps'); ?></h3>
            <p class="description"><?php _e('Select which boundaries to include in your map.', 'dakoii-maps'); ?></p>

            <div class="boundary-selection" data-level="<?php echo esc_attr($map_level); ?>">

                <!-- Provincial Level Selection -->
                <div class="boundary-level-section" data-level="provincial" style="<?php echo ($map_level !== 'provincial') ? 'display: none;' : ''; ?>">
                    <h4><?php _e('Select Provinces', 'dakoii-maps'); ?></h4>
                    <p class="description"><?php _e('Choose which provinces to include in your map. Users will be able to click on provinces to drill down to districts.', 'dakoii-maps'); ?></p>
                    <div class="boundary-options provincial-options">
                        <div class="loading"><?php _e('Loading provinces...', 'dakoii-maps'); ?></div>
                    </div>
                    <div class="boundary-actions">
                        <button type="button" class="button select-all-boundaries" data-level="provincial"><?php _e('Select All', 'dakoii-maps'); ?></button>
                        <button type="button" class="button select-none-boundaries" data-level="provincial"><?php _e('Select None', 'dakoii-maps'); ?></button>
                    </div>
                </div>

                <!-- District Level Selection -->
                <div class="boundary-level-section" data-level="district" style="<?php echo ($map_level !== 'district') ? 'display: none;' : ''; ?>">
                    <h4><?php _e('Select Districts', 'dakoii-maps'); ?></h4>
                    <p class="description"><?php _e('Choose which districts to include in your map. Users will be able to click on districts to drill down to LLGs.', 'dakoii-maps'); ?></p>

                    <!-- Province Filter for Districts -->
                    <div class="parent-filter">
                        <label for="province-filter"><?php _e('Filter by Province (optional):', 'dakoii-maps'); ?></label>
                        <select id="province-filter" class="parent-boundary-filter" data-child-level="district">
                            <option value=""><?php _e('All Provinces', 'dakoii-maps'); ?></option>
                        </select>
                    </div>

                    <div class="boundary-options district-options">
                        <div class="loading"><?php _e('Loading districts...', 'dakoii-maps'); ?></div>
                    </div>
                    <div class="boundary-actions">
                        <button type="button" class="button select-all-boundaries" data-level="district"><?php _e('Select All', 'dakoii-maps'); ?></button>
                        <button type="button" class="button select-none-boundaries" data-level="district"><?php _e('Select None', 'dakoii-maps'); ?></button>
                    </div>
                </div>

                <!-- LLG Level Selection -->
                <div class="boundary-level-section" data-level="llg" style="<?php echo ($map_level !== 'llg') ? 'display: none;' : ''; ?>">
                    <h4><?php _e('Select Local Level Governments (LLGs)', 'dakoii-maps'); ?></h4>
                    <p class="description"><?php _e('Choose which LLGs to include in your map. This is the most detailed administrative level.', 'dakoii-maps'); ?></p>

                    <!-- Province Filter for LLGs -->
                    <div class="parent-filter">
                        <label for="province-filter-llg"><?php _e('Filter by Province (optional):', 'dakoii-maps'); ?></label>
                        <select id="province-filter-llg" class="parent-boundary-filter" data-child-level="llg" data-filter-type="province">
                            <option value=""><?php _e('All Provinces', 'dakoii-maps'); ?></option>
                        </select>
                    </div>

                    <!-- District Filter for LLGs -->
                    <div class="parent-filter">
                        <label for="district-filter-llg"><?php _e('Filter by District (optional):', 'dakoii-maps'); ?></label>
                        <select id="district-filter-llg" class="parent-boundary-filter" data-child-level="llg" data-filter-type="district">
                            <option value=""><?php _e('All Districts', 'dakoii-maps'); ?></option>
                        </select>
                    </div>

                    <div class="boundary-options llg-options">
                        <div class="loading"><?php _e('Loading LLGs...', 'dakoii-maps'); ?></div>
                    </div>
                    <div class="boundary-actions">
                        <button type="button" class="button select-all-boundaries" data-level="llg"><?php _e('Select All', 'dakoii-maps'); ?></button>
                        <button type="button" class="button select-none-boundaries" data-level="llg"><?php _e('Select None', 'dakoii-maps'); ?></button>
                    </div>
                </div>

                <!-- Selection Summary -->
                <div class="selection-summary">
                    <p class="selected-count-wrapper">
                        <strong><?php _e('Selected:', 'dakoii-maps'); ?></strong> <span class="selected-count">0</span>
                        <span class="boundary-type-label"></span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Display Options Section -->
        <div class="dakoii-form-section">
            <h3><?php _e('Display Options', 'dakoii-maps'); ?></h3>
            <p class="description"><?php _e('Configure how your map will be displayed.', 'dakoii-maps'); ?></p>

            <div class="dakoii-form-columns">
                <div class="dakoii-form-column">
                    <div class="dakoii-form-row">
                        <label for="map_width"><?php _e('Map Width', 'dakoii-maps'); ?></label>
                        <input type="text" id="map_width" name="map_width" 
                               value="<?php echo esc_attr($config['display_options']['width']); ?>" 
                               placeholder="100%">
                        <p class="form-help"><?php _e('Width in pixels (e.g., 800px) or percentage (e.g., 100%).', 'dakoii-maps'); ?></p>
                    </div>

                    <div class="dakoii-form-row">
                        <label for="map_height"><?php _e('Map Height', 'dakoii-maps'); ?></label>
                        <input type="text" id="map_height" name="map_height" 
                               value="<?php echo esc_attr($config['display_options']['height']); ?>" 
                               placeholder="500px">
                        <p class="form-help"><?php _e('Height in pixels (e.g., 500px).', 'dakoii-maps'); ?></p>
                    </div>
                </div>

                <div class="dakoii-form-column">
                    <div class="dakoii-form-row">
                        <label for="initial_zoom"><?php _e('Initial Zoom Level', 'dakoii-maps'); ?></label>
                        <input type="number" id="initial_zoom" name="initial_zoom" min="1" max="12" 
                               value="<?php echo esc_attr($config['display_options']['zoom']['initial']); ?>">
                        <p class="form-help"><?php _e('Starting zoom level (1-12, where 6 is recommended for PNG).', 'dakoii-maps'); ?></p>
                    </div>

                    <div class="dakoii-form-row">
                        <label for="min_zoom"><?php _e('Minimum Zoom', 'dakoii-maps'); ?></label>
                        <input type="number" id="min_zoom" name="min_zoom" min="1" max="12" 
                               value="<?php echo esc_attr($config['display_options']['zoom']['min']); ?>">
                    </div>

                    <div class="dakoii-form-row">
                        <label for="max_zoom"><?php _e('Maximum Zoom', 'dakoii-maps'); ?></label>
                        <input type="number" id="max_zoom" name="max_zoom" min="1" max="12" 
                               value="<?php echo esc_attr($config['display_options']['zoom']['max']); ?>">
                    </div>
                </div>
            </div>

            <div class="dakoii-form-columns">
                <div class="dakoii-form-column">
                    <div class="dakoii-form-row">
                        <label for="center_lat"><?php _e('Center Latitude', 'dakoii-maps'); ?></label>
                        <input type="number" id="center_lat" name="center_lat" step="0.000001" 
                               value="<?php echo esc_attr($config['display_options']['center']['lat']); ?>">
                        <p class="form-help"><?php _e('Map center latitude (-6.314993 for PNG center).', 'dakoii-maps'); ?></p>
                    </div>
                </div>

                <div class="dakoii-form-column">
                    <div class="dakoii-form-row">
                        <label for="center_lng"><?php _e('Center Longitude', 'dakoii-maps'); ?></label>
                        <input type="number" id="center_lng" name="center_lng" step="0.000001" 
                               value="<?php echo esc_attr($config['display_options']['center']['lng']); ?>">
                        <p class="form-help"><?php _e('Map center longitude (143.95555 for PNG center).', 'dakoii-maps'); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Styling Options Section -->
        <div class="dakoii-form-section">
            <h3><?php _e('Styling Options', 'dakoii-maps'); ?></h3>
            <p class="description"><?php _e('Customize the appearance of your map boundaries.', 'dakoii-maps'); ?></p>

            <div class="styling-options">
                <div class="dakoii-form-row">
                    <label for="boundary_color"><?php _e('Boundary Color', 'dakoii-maps'); ?></label>
                    <div class="color-picker-wrapper">
                        <input type="color" id="boundary_color_picker" 
                               value="<?php echo esc_attr($config['styling']['boundary_color']); ?>">
                        <input type="text" id="boundary_color" name="boundary_color" 
                               value="<?php echo esc_attr($config['styling']['boundary_color']); ?>" 
                               placeholder="#3388ff">
                    </div>
                </div>

                <div class="dakoii-form-row">
                    <label for="boundary_width"><?php _e('Boundary Width', 'dakoii-maps'); ?></label>
                    <input type="number" id="boundary_width" name="boundary_width" min="1" max="10" 
                           value="<?php echo esc_attr($config['styling']['boundary_width']); ?>">
                    <p class="form-help"><?php _e('Line width in pixels (1-10).', 'dakoii-maps'); ?></p>
                </div>

                <div class="dakoii-form-row">
                    <label for="fill_color"><?php _e('Fill Color', 'dakoii-maps'); ?></label>
                    <div class="color-picker-wrapper">
                        <input type="color" id="fill_color_picker" 
                               value="<?php echo esc_attr($config['styling']['fill_color']); ?>">
                        <input type="text" id="fill_color" name="fill_color" 
                               value="<?php echo esc_attr($config['styling']['fill_color']); ?>" 
                               placeholder="#3388ff">
                    </div>
                </div>

                <div class="dakoii-form-row">
                    <label for="fill_opacity"><?php _e('Fill Opacity', 'dakoii-maps'); ?></label>
                    <input type="number" id="fill_opacity" name="fill_opacity" min="0" max="1" step="0.1" 
                           value="<?php echo esc_attr($config['styling']['fill_opacity']); ?>">
                    <p class="form-help"><?php _e('Fill transparency (0.0 = transparent, 1.0 = opaque).', 'dakoii-maps'); ?></p>
                </div>

                <div class="dakoii-form-row">
                    <label for="hover_color"><?php _e('Hover Color', 'dakoii-maps'); ?></label>
                    <div class="color-picker-wrapper">
                        <input type="color" id="hover_color_picker" 
                               value="<?php echo esc_attr($config['styling']['hover_color']); ?>">
                        <input type="text" id="hover_color" name="hover_color" 
                               value="<?php echo esc_attr($config['styling']['hover_color']); ?>" 
                               placeholder="#ff7800">
                    </div>
                </div>

                <div class="dakoii-form-row">
                    <label for="hover_opacity"><?php _e('Hover Opacity', 'dakoii-maps'); ?></label>
                    <input type="number" id="hover_opacity" name="hover_opacity" min="0" max="1" step="0.1" 
                           value="<?php echo esc_attr($config['styling']['hover_opacity']); ?>">
                    <p class="form-help"><?php _e('Hover transparency (0.0 = transparent, 1.0 = opaque).', 'dakoii-maps'); ?></p>
                </div>
            </div>
        </div>

        <!-- Feature Options Section -->
        <div class="dakoii-form-section">
            <h3><?php _e('Feature Options', 'dakoii-maps'); ?></h3>
            <p class="description"><?php _e('Enable or disable interactive features for your map.', 'dakoii-maps'); ?></p>

            <div class="feature-toggles">
                <div class="feature-toggle">
                    <input type="checkbox" id="popup_enabled" name="popup_enabled" 
                           <?php checked($config['features']['popup_enabled']); ?>>
                    <label for="popup_enabled"><?php _e('Enable Information Popups', 'dakoii-maps'); ?></label>
                </div>

                <div class="feature-toggle">
                    <input type="checkbox" id="click_navigation" name="click_navigation" 
                           <?php checked($config['features']['click_navigation']); ?>>
                    <label for="click_navigation"><?php _e('Enable Click Navigation', 'dakoii-maps'); ?></label>
                </div>

                <div class="feature-toggle">
                    <input type="checkbox" id="search_enabled" name="search_enabled" 
                           <?php checked($config['features']['search_enabled']); ?>>
                    <label for="search_enabled"><?php _e('Enable Search (Coming Soon)', 'dakoii-maps'); ?></label>
                </div>

                <div class="feature-toggle">
                    <input type="checkbox" id="legend_enabled" name="legend_enabled" 
                           <?php checked($config['features']['legend_enabled']); ?>>
                    <label for="legend_enabled"><?php _e('Show Legend', 'dakoii-maps'); ?></label>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="dakoii-form-section">
            <div style="display: flex; gap: 10px; align-items: center;">
                <input type="submit" class="button button-primary" 
                       value="<?php echo $is_edit ? __('Update Map', 'dakoii-maps') : __('Create Map', 'dakoii-maps'); ?>">
                
                <button type="button" class="button preview-map">
                    <?php _e('Preview Map', 'dakoii-maps'); ?>
                </button>
                
                <a href="<?php echo admin_url('admin.php?page=dakoii-maps'); ?>" class="button">
                    <?php _e('Cancel', 'dakoii-maps'); ?>
                </a>
            </div>
        </div>

        <?php if ($is_edit): ?>
            <!-- Shortcode Display -->
            <div class="shortcode-display">
                <h4><?php _e('Shortcode', 'dakoii-maps'); ?></h4>
                <div class="shortcode-code">[<?php echo esc_html($map->shortcode); ?>]</div>
                <div class="shortcode-actions">
                    <button type="button" class="button copy-shortcode" 
                            data-shortcode="[<?php echo esc_attr($map->shortcode); ?>]">
                        <?php _e('Copy Shortcode', 'dakoii-maps'); ?>
                    </button>
                </div>
                <p class="form-help">
                    <?php _e('Use this shortcode to display the map in any post or page.', 'dakoii-maps'); ?>
                </p>
            </div>
        <?php endif; ?>
    </form>
</div>
