<?php
/**
 * Admin Interface Class
 * Handles WordPress admin interface for the plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DakoiiAdminInterface {
    
    /**
     * Map manager instance
     */
    private $map_manager;
    
    /**
     * Boundary loader instance
     */
    private $boundary_loader;
    
    /**
     * Cache manager instance
     */
    private $cache_manager;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->map_manager = new DakoiiMapManager();
        $this->boundary_loader = new DakoniiBoundaryLoader();
        $this->cache_manager = new DakoiiCacheManager();
        
        // Admin hooks
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        
        // AJAX hooks
        add_action('wp_ajax_dakoii_get_boundary_options', array($this, 'ajax_get_boundary_options'));
        add_action('wp_ajax_dakoii_preview_map', array($this, 'ajax_preview_map'));
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Register settings
        register_setting('dakoii_maps_settings', 'dakoii_maps_settings', array(
            'sanitize_callback' => array($this, 'sanitize_settings')
        ));
        
        // Add settings sections
        $this->add_settings_sections();
        
        // Handle form submissions
        $this->handle_form_submissions();
    }
    
    /**
     * Add admin menu
     */
    public function admin_menu() {
        // Main menu page
        $main_page = add_menu_page(
            __('Dakoii Provincial Map Boundries', 'dakoii-maps'),
            __('Dakoii Maps', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps',
            array($this, 'admin_page_maps'),
            'dashicons-location-alt',
            30
        );
        
        // Submenu pages
        add_submenu_page(
            'dakoii-maps',
            __('All Maps', 'dakoii-maps'),
            __('All Maps', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps',
            array($this, 'admin_page_maps')
        );
        
        add_submenu_page(
            'dakoii-maps',
            __('Add New Map', 'dakoii-maps'),
            __('Add New Map', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps-add',
            array($this, 'admin_page_add_map')
        );
        
        add_submenu_page(
            'dakoii-maps',
            __('Edit Map', 'dakoii-maps'),
            null, // Hidden from menu
            'manage_options',
            'dakoii-maps-edit',
            array($this, 'admin_page_edit_map')
        );
        
        add_submenu_page(
            'dakoii-maps',
            __('Settings', 'dakoii-maps'),
            __('Settings', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps-settings',
            array($this, 'admin_page_settings')
        );
        
        // Add help tab to main page
        add_action('load-' . $main_page, array($this, 'add_help_tabs'));
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        // Only load on plugin pages
        if (strpos($hook, 'dakoii-maps') === false) {
            return;
        }
        
        // Admin CSS
        wp_enqueue_style(
            'dakoii-maps-admin',
            DAKOII_MAPS_PLUGIN_URL . 'assets/css/admin-style.css',
            array(),
            DAKOII_MAPS_VERSION
        );
        
        // WordPress media library for future use
        wp_enqueue_media();
        
        // Admin JS
        wp_enqueue_script(
            'dakoii-maps-admin',
            DAKOII_MAPS_PLUGIN_URL . 'assets/js/admin-script.js',
            array('jquery', 'jquery-ui-sortable'),
            DAKOII_MAPS_VERSION,
            true
        );
        
        // Localize script
        wp_localize_script('dakoii-maps-admin', 'dakoiiMapsAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dakoii_maps_admin_nonce'),
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this map?', 'dakoii-maps'),
                'loading' => __('Loading...', 'dakoii-maps'),
                'error' => __('An error occurred. Please try again.', 'dakoii-maps'),
                'success' => __('Operation completed successfully.', 'dakoii-maps'),
                'selectBoundaries' => __('Please select at least one boundary.', 'dakoii-maps'),
                'invalidConfig' => __('Invalid map configuration.', 'dakoii-maps')
            )
        ));
    }
    
    /**
     * Admin page: All Maps
     */
    public function admin_page_maps() {
        // Handle bulk actions
        $this->handle_bulk_actions();
        
        // Get maps with pagination
        $per_page = 20;
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $offset = ($current_page - 1) * $per_page;
        
        $maps = $this->map_manager->get_all_maps(array(
            'limit' => $per_page,
            'offset' => $offset
        ));
        
        $total_maps = $this->map_manager->get_maps_count();
        
        // Load template
        include DAKOII_MAPS_PLUGIN_DIR . 'templates/admin/map-list.php';
    }
    
    /**
     * Admin page: Add New Map
     */
    public function admin_page_add_map() {
        $map = null;
        $mode = 'add';
        
        // Load template
        include DAKOII_MAPS_PLUGIN_DIR . 'templates/admin/map-form.php';
    }
    
    /**
     * Admin page: Edit Map
     */
    public function admin_page_edit_map() {
        $map_id = isset($_GET['map_id']) ? intval($_GET['map_id']) : 0;
        
        if (!$map_id) {
            wp_die(__('Invalid map ID', 'dakoii-maps'));
        }
        
        $map = $this->map_manager->get_map($map_id);
        
        if (!$map) {
            wp_die(__('Map not found', 'dakoii-maps'));
        }
        
        $mode = 'edit';
        
        // Load template
        include DAKOII_MAPS_PLUGIN_DIR . 'templates/admin/map-form.php';
    }
    
    /**
     * Admin page: Settings
     */
    public function admin_page_settings() {
        $settings = get_option('dakoii_maps_settings', array());
        $cache_stats = $this->cache_manager->get_cache_stats();
        
        // Load template
        include DAKOII_MAPS_PLUGIN_DIR . 'templates/admin/settings.php';
    }
    
    /**
     * Add settings sections
     */
    private function add_settings_sections() {
        // General settings section
        add_settings_section(
            'dakoii_maps_general',
            __('General Settings', 'dakoii-maps'),
            array($this, 'settings_section_general'),
            'dakoii_maps_settings'
        );
        
        // Cache settings section
        add_settings_section(
            'dakoii_maps_cache',
            __('Cache Settings', 'dakoii-maps'),
            array($this, 'settings_section_cache'),
            'dakoii_maps_settings'
        );
        
        // Default map settings section
        add_settings_section(
            'dakoii_maps_defaults',
            __('Default Map Settings', 'dakoii-maps'),
            array($this, 'settings_section_defaults'),
            'dakoii_maps_settings'
        );
    }
    
    /**
     * Settings section: General
     */
    public function settings_section_general() {
        echo '<p>' . __('Configure general settings for the Dakoii Maps plugin.', 'dakoii-maps') . '</p>';
    }
    
    /**
     * Settings section: Cache
     */
    public function settings_section_cache() {
        echo '<p>' . __('Configure caching settings to improve performance.', 'dakoii-maps') . '</p>';
    }
    
    /**
     * Settings section: Defaults
     */
    public function settings_section_defaults() {
        echo '<p>' . __('Set default values for new maps.', 'dakoii-maps') . '</p>';
    }
    
    /**
     * Sanitize settings
     */
    public function sanitize_settings($settings) {
        $sanitized = array();
        
        // Cache settings
        $sanitized['cache_enabled'] = isset($settings['cache_enabled']) ? (bool)$settings['cache_enabled'] : true;
        $sanitized['cache_expiry'] = isset($settings['cache_expiry']) ? max(300, intval($settings['cache_expiry'])) : 3600;
        
        // Default map settings
        $sanitized['default_map_width'] = isset($settings['default_map_width']) ? sanitize_text_field($settings['default_map_width']) : '100%';
        $sanitized['default_map_height'] = isset($settings['default_map_height']) ? sanitize_text_field($settings['default_map_height']) : '500px';
        $sanitized['default_zoom_level'] = isset($settings['default_zoom_level']) ? max(1, min(12, intval($settings['default_zoom_level']))) : 6;
        
        // Debug settings
        $sanitized['enable_debug'] = isset($settings['enable_debug']) ? (bool)$settings['enable_debug'] : false;
        
        return $sanitized;
    }
    
    /**
     * Handle form submissions
     */
    private function handle_form_submissions() {
        // Handle map creation/update
        if (isset($_POST['dakoii_save_map']) && wp_verify_nonce($_POST['dakoii_map_nonce'], 'dakoii_save_map')) {
            $this->handle_save_map();
        }
        
        // Handle map deletion
        if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['map_id'])) {
            $this->handle_delete_map();
        }
    }
    
    /**
     * Handle save map
     */
    private function handle_save_map() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $map_id = isset($_POST['map_id']) ? intval($_POST['map_id']) : 0;
        $config = $this->build_map_config_from_post();
        
        if ($map_id) {
            // Update existing map
            $success = $this->map_manager->update_map($map_id, $config);
            $message = $success ? __('Map updated successfully.', 'dakoii-maps') : __('Failed to update map.', 'dakoii-maps');
        } else {
            // Create new map
            $map_id = $this->map_manager->create_map($config);
            $success = $map_id !== false;
            $message = $success ? __('Map created successfully.', 'dakoii-maps') : __('Failed to create map.', 'dakoii-maps');
        }
        
        // Redirect with message
        $redirect_url = admin_url('admin.php?page=dakoii-maps');
        if ($success) {
            $redirect_url = add_query_arg('message', 'saved', $redirect_url);
            if (!isset($_POST['map_id'])) {
                $redirect_url = add_query_arg('map_id', $map_id, $redirect_url);
            }
        } else {
            $redirect_url = add_query_arg('error', 'save_failed', $redirect_url);
        }
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Handle delete map
     */
    private function handle_delete_map() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        if (!wp_verify_nonce($_GET['_wpnonce'], 'delete_map_' . $_GET['map_id'])) {
            wp_die(__('Security check failed', 'dakoii-maps'));
        }
        
        $map_id = intval($_GET['map_id']);
        $success = $this->map_manager->delete_map($map_id);
        
        $redirect_url = admin_url('admin.php?page=dakoii-maps');
        if ($success) {
            $redirect_url = add_query_arg('message', 'deleted', $redirect_url);
        } else {
            $redirect_url = add_query_arg('error', 'delete_failed', $redirect_url);
        }
        
        wp_redirect($redirect_url);
        exit;
    }
    
    /**
     * Handle bulk actions
     */
    private function handle_bulk_actions() {
        if (!isset($_POST['action']) || !isset($_POST['map_ids'])) {
            return;
        }
        
        if (!wp_verify_nonce($_POST['_wpnonce'], 'bulk-maps')) {
            wp_die(__('Security check failed', 'dakoii-maps'));
        }
        
        $action = sanitize_text_field($_POST['action']);
        $map_ids = array_map('intval', $_POST['map_ids']);
        
        switch ($action) {
            case 'delete':
                $deleted = 0;
                foreach ($map_ids as $map_id) {
                    if ($this->map_manager->delete_map($map_id)) {
                        $deleted++;
                    }
                }
                
                $redirect_url = admin_url('admin.php?page=dakoii-maps');
                $redirect_url = add_query_arg('message', 'bulk_deleted', $redirect_url);
                $redirect_url = add_query_arg('deleted_count', $deleted, $redirect_url);
                
                wp_redirect($redirect_url);
                exit;
                break;
        }
    }
    
    /**
     * Build map config from POST data
     */
    private function build_map_config_from_post() {
        $config = array(
            'name' => sanitize_text_field($_POST['map_name'] ?? ''),
            'description' => sanitize_textarea_field($_POST['map_description'] ?? ''),
            'map_level' => sanitize_text_field($_POST['map_level'] ?? 'provincial'),
            'boundaries' => array(),
            'display_options' => array(
                'width' => sanitize_text_field($_POST['map_width'] ?? '100%'),
                'height' => sanitize_text_field($_POST['map_height'] ?? '500px'),
                'zoom' => array(
                    'initial' => intval($_POST['initial_zoom'] ?? 6),
                    'min' => intval($_POST['min_zoom'] ?? 4),
                    'max' => intval($_POST['max_zoom'] ?? 12)
                ),
                'center' => array(
                    'lat' => floatval($_POST['center_lat'] ?? -6.314993),
                    'lng' => floatval($_POST['center_lng'] ?? 143.95555)
                )
            ),
            'styling' => array(
                'boundary_color' => sanitize_hex_color($_POST['boundary_color'] ?? '#3388ff'),
                'boundary_width' => intval($_POST['boundary_width'] ?? 2),
                'fill_color' => sanitize_hex_color($_POST['fill_color'] ?? '#3388ff'),
                'fill_opacity' => floatval($_POST['fill_opacity'] ?? 0.2),
                'hover_color' => sanitize_hex_color($_POST['hover_color'] ?? '#ff7800'),
                'hover_opacity' => floatval($_POST['hover_opacity'] ?? 0.7)
            ),
            'features' => array(
                'popup_enabled' => isset($_POST['popup_enabled']),
                'click_navigation' => isset($_POST['click_navigation']),
                'search_enabled' => isset($_POST['search_enabled']),
                'legend_enabled' => isset($_POST['legend_enabled'])
            )
        );
        
        // Process selected boundaries
        if (isset($_POST['selected_boundaries']) && is_array($_POST['selected_boundaries'])) {
            foreach ($_POST['selected_boundaries'] as $boundary_data) {
                $boundary = json_decode(stripslashes($boundary_data), true);
                if ($boundary) {
                    $config['boundaries'][] = $boundary;
                }
            }
        }
        
        return $config;
    }
    
    /**
     * Add help tabs
     */
    public function add_help_tabs() {
        $screen = get_current_screen();
        
        $screen->add_help_tab(array(
            'id' => 'dakoii_maps_overview',
            'title' => __('Overview', 'dakoii-maps'),
            'content' => '<p>' . __('The Dakoii Maps plugin allows you to create interactive maps of Papua New Guinea administrative boundaries.', 'dakoii-maps') . '</p>'
        ));
        
        $screen->add_help_tab(array(
            'id' => 'dakoii_maps_creating',
            'title' => __('Creating Maps', 'dakoii-maps'),
            'content' => '<p>' . __('To create a new map, click "Add New Map" and follow the step-by-step wizard.', 'dakoii-maps') . '</p>'
        ));
        
        $screen->set_help_sidebar(
            '<p><strong>' . __('For more information:', 'dakoii-maps') . '</strong></p>' .
            '<p><a href="#" target="_blank">' . __('Plugin Documentation', 'dakoii-maps') . '</a></p>'
        );
    }
    
    /**
     * AJAX: Get boundary options
     */
    public function ajax_get_boundary_options() {
        check_ajax_referer('dakoii_maps_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $type = sanitize_text_field($_POST['type'] ?? '');
        $parent_codes = $_POST['parent_codes'] ?? array();
        
        if (empty($type)) {
            wp_send_json_error(__('Boundary type is required', 'dakoii-maps'));
        }
        
        $boundaries = $this->boundary_loader->load_boundaries($type, array(
            'geocode_prefix' => $parent_codes
        ));
        
        $options = array();
        
        if (isset($boundaries['features'])) {
            foreach ($boundaries['features'] as $feature) {
                if (isset($feature['properties'])) {
                    $props = $feature['properties'];
                    $name = '';
                    $geocode = '';
                    
                    switch ($type) {
                        case 'provincial':
                            $name = $props['PROVNAME'] ?? '';
                            $geocode = $props['PROVID'] ?? '';
                            break;
                        case 'district':
                            $name = $props['DISTNAME'] ?? '';
                            $geocode = $props['GEOCODE'] ?? '';
                            break;
                        case 'llg':
                            $name = $props['LLGNAME'] ?? '';
                            $geocode = $props['GEOCODE'] ?? '';
                            break;
                    }
                    
                    if ($name && $geocode) {
                        $options[] = array(
                            'id' => $props['FID'] ?? '',
                            'name' => $name,
                            'geocode' => $geocode,
                            'type' => $type
                        );
                    }
                }
            }
        }
        
        wp_send_json_success($options);
    }
    
    /**
     * AJAX: Preview map
     */
    public function ajax_preview_map() {
        check_ajax_referer('dakoii_maps_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $config = $_POST['config'] ?? array();
        
        // Validate and sanitize config
        // This would include the same validation as in the map manager
        
        wp_send_json_success(array(
            'preview_url' => '#', // Would generate preview URL
            'message' => __('Preview generated successfully', 'dakoii-maps')
        ));
    }
}
