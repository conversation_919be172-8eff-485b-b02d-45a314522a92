<?php
/**
 * Map Manager Class
 * Handles all map-related operations including CRUD operations
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DakoiiMapManager {
    
    /**
     * Maps table name
     */
    private $maps_table;
    
    /**
     * Boundaries table name
     */
    private $boundaries_table;
    
    /**
     * Constructor
     */
    public function __construct() {
        global $wpdb;
        $this->maps_table = $wpdb->prefix . 'dakoii_maps';
        $this->boundaries_table = $wpdb->prefix . 'dakoii_map_boundaries';
        
        // Add AJAX hooks
        add_action('wp_ajax_dakoii_create_map', array($this, 'ajax_create_map'));
        add_action('wp_ajax_dakoii_update_map', array($this, 'ajax_update_map'));
        add_action('wp_ajax_dakoii_delete_map', array($this, 'ajax_delete_map'));
        add_action('wp_ajax_dakoii_get_map', array($this, 'ajax_get_map'));
    }
    
    /**
     * Create a new map
     * 
     * @param array $config Map configuration
     * @return int|false Map ID on success, false on failure
     */
    public function create_map($config) {
        // Validate configuration
        $validated_config = $this->validate_map_config($config);
        if (!$validated_config) {
            return false;
        }
        
        // Generate unique shortcode
        $shortcode = $this->generate_unique_shortcode();
        
        global $wpdb;
        
        // Insert map record
        $result = $wpdb->insert(
            $this->maps_table,
            array(
                'name' => sanitize_text_field($config['name']),
                'description' => sanitize_textarea_field($config['description']),
                'map_level' => sanitize_text_field($config['map_level']),
                'config' => wp_json_encode($validated_config),
                'shortcode' => $shortcode,
                'status' => 'active',
                'created_by' => get_current_user_id()
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%d')
        );
        
        if ($result) {
            $map_id = $wpdb->insert_id;
            
            // Save map boundaries
            if (isset($config['boundaries']) && is_array($config['boundaries'])) {
                $this->save_map_boundaries($map_id, $config['boundaries']);
            }
            
            // Fire action hook
            do_action('dakoii_map_created', $map_id, $config);
            
            return $map_id;
        }
        
        return false;
    }
    
    /**
     * Update an existing map
     * 
     * @param int $map_id Map ID
     * @param array $config Map configuration
     * @return bool Success status
     */
    public function update_map($map_id, $config) {
        // Validate configuration
        $validated_config = $this->validate_map_config($config);
        if (!$validated_config) {
            return false;
        }
        
        global $wpdb;
        
        // Update map record
        $result = $wpdb->update(
            $this->maps_table,
            array(
                'name' => sanitize_text_field($config['name']),
                'description' => sanitize_textarea_field($config['description']),
                'map_level' => sanitize_text_field($config['map_level']),
                'config' => wp_json_encode($validated_config),
                'status' => isset($config['status']) ? sanitize_text_field($config['status']) : 'active'
            ),
            array('id' => $map_id),
            array('%s', '%s', '%s', '%s', '%s'),
            array('%d')
        );
        
        if ($result !== false) {
            // Update map boundaries
            if (isset($config['boundaries']) && is_array($config['boundaries'])) {
                $this->save_map_boundaries($map_id, $config['boundaries']);
            }
            
            // Fire action hook
            do_action('dakoii_map_updated', $map_id, $config);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Delete a map
     * 
     * @param int $map_id Map ID
     * @return bool Success status
     */
    public function delete_map($map_id) {
        global $wpdb;
        
        // Get map data before deletion for hooks
        $map = $this->get_map($map_id);
        
        if (!$map) {
            return false;
        }
        
        // Delete map boundaries (cascade should handle this, but let's be explicit)
        $wpdb->delete($this->boundaries_table, array('map_id' => $map_id), array('%d'));
        
        // Delete map
        $result = $wpdb->delete($this->maps_table, array('id' => $map_id), array('%d'));
        
        if ($result) {
            // Fire action hook
            do_action('dakoii_map_deleted', $map_id, $map);
            return true;
        }
        
        return false;
    }
    
    /**
     * Get a map by ID
     * 
     * @param int $map_id Map ID
     * @return object|null Map object or null
     */
    public function get_map($map_id) {
        global $wpdb;
        
        $map = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->maps_table} WHERE id = %d", $map_id)
        );
        
        if ($map) {
            // Decode JSON config
            $map->config = json_decode($map->config, true);
            
            // Get boundaries
            $map->boundaries = $this->get_map_boundaries($map_id);
        }
        
        return $map;
    }
    
    /**
     * Get a map by shortcode
     * 
     * @param string $shortcode Shortcode
     * @return object|null Map object or null
     */
    public function get_map_by_shortcode($shortcode) {
        global $wpdb;
        
        $map = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->maps_table} WHERE shortcode = %s", $shortcode)
        );
        
        if ($map) {
            // Decode JSON config
            $map->config = json_decode($map->config, true);
            
            // Get boundaries
            $map->boundaries = $this->get_map_boundaries($map->id);
        }
        
        return $map;
    }
    
    /**
     * Get all maps
     * 
     * @param array $args Query arguments
     * @return array Maps array
     */
    public function get_all_maps($args = array()) {
        global $wpdb;
        
        $defaults = array(
            'status' => 'active',
            'orderby' => 'created_at',
            'order' => 'DESC',
            'limit' => 20,
            'offset' => 0
        );
        
        $args = wp_parse_args($args, $defaults);
        
        $where = '';
        $where_values = array();
        
        if ($args['status'] && $args['status'] !== 'all') {
            $where = 'WHERE status = %s';
            $where_values[] = $args['status'];
        }
        
        $orderby = sanitize_sql_orderby($args['orderby'] . ' ' . $args['order']);
        $limit = '';
        
        if ($args['limit'] > 0) {
            $limit = $wpdb->prepare('LIMIT %d OFFSET %d', $args['limit'], $args['offset']);
        }
        
        $query = "SELECT * FROM {$this->maps_table} {$where} ORDER BY {$orderby} {$limit}";
        
        if (!empty($where_values)) {
            $query = $wpdb->prepare($query, $where_values);
        }
        
        $maps = $wpdb->get_results($query);
        
        // Decode JSON configs
        foreach ($maps as $map) {
            $map->config = json_decode($map->config, true);
        }
        
        return $maps;
    }
    
    /**
     * Get maps count
     * 
     * @param string $status Status filter
     * @return int Maps count
     */
    public function get_maps_count($status = 'active') {
        global $wpdb;
        
        if ($status === 'all') {
            return $wpdb->get_var("SELECT COUNT(*) FROM {$this->maps_table}");
        }
        
        return $wpdb->get_var(
            $wpdb->prepare("SELECT COUNT(*) FROM {$this->maps_table} WHERE status = %s", $status)
        );
    }
    
    /**
     * Validate map configuration
     * 
     * @param array $config Configuration to validate
     * @return array|false Validated config or false
     */
    private function validate_map_config($config) {
        $required_fields = array('name', 'map_level');
        
        // Check required fields
        foreach ($required_fields as $field) {
            if (!isset($config[$field]) || empty($config[$field])) {
                return false;
            }
        }
        
        // Validate map level
        $valid_levels = array('provincial', 'district', 'llg');
        if (!in_array($config['map_level'], $valid_levels)) {
            return false;
        }
        
        // Set defaults for optional fields
        $defaults = array(
            'description' => '',
            'display_options' => $this->get_default_display_options(),
            'styling' => $this->get_default_styling(),
            'features' => $this->get_default_features()
        );
        
        return wp_parse_args($config, $defaults);
    }
    
    /**
     * Get default display options
     * 
     * @return array Default display configuration
     */
    private function get_default_display_options() {
        return array(
            'width' => '100%',
            'height' => '500px',
            'zoom' => array(
                'initial' => 6,
                'min' => 4,
                'max' => 12
            ),
            'center' => array(
                'lat' => -6.314993,
                'lng' => 143.95555
            )
        );
    }
    
    /**
     * Get default styling options
     * 
     * @return array Default styling configuration
     */
    private function get_default_styling() {
        return array(
            'boundary_color' => '#3388ff',
            'boundary_width' => 2,
            'fill_color' => '#3388ff',
            'fill_opacity' => 0.2,
            'hover_color' => '#ff7800',
            'hover_opacity' => 0.7
        );
    }
    
    /**
     * Get default features options
     * 
     * @return array Default features configuration
     */
    private function get_default_features() {
        return array(
            'popup_enabled' => true,
            'click_navigation' => true,
            'search_enabled' => false,
            'legend_enabled' => false
        );
    }
    
    /**
     * Generate unique shortcode
     * 
     * @return string Unique shortcode
     */
    private function generate_unique_shortcode() {
        global $wpdb;
        
        do {
            $shortcode = 'dakoii_map_' . wp_generate_password(8, false);
            $exists = $wpdb->get_var(
                $wpdb->prepare("SELECT COUNT(*) FROM {$this->maps_table} WHERE shortcode = %s", $shortcode)
            );
        } while ($exists > 0);
        
        return $shortcode;
    }
    
    /**
     * Save map boundaries
     * 
     * @param int $map_id Map ID
     * @param array $boundaries Boundaries array
     */
    private function save_map_boundaries($map_id, $boundaries) {
        global $wpdb;
        
        // Clear existing boundaries
        $wpdb->delete($this->boundaries_table, array('map_id' => $map_id), array('%d'));
        
        // Insert new boundaries
        foreach ($boundaries as $index => $boundary) {
            $wpdb->insert(
                $this->boundaries_table,
                array(
                    'map_id' => $map_id,
                    'boundary_type' => sanitize_text_field($boundary['type']),
                    'boundary_id' => sanitize_text_field($boundary['id']),
                    'boundary_name' => sanitize_text_field($boundary['name']),
                    'geocode' => isset($boundary['geocode']) ? sanitize_text_field($boundary['geocode']) : '',
                    'display_order' => $index,
                    'is_active' => isset($boundary['active']) ? (bool)$boundary['active'] : true
                ),
                array('%d', '%s', '%s', '%s', '%s', '%d', '%d')
            );
        }
    }
    
    /**
     * Get map boundaries
     * 
     * @param int $map_id Map ID
     * @return array Boundaries array
     */
    private function get_map_boundaries($map_id) {
        global $wpdb;
        
        return $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$this->boundaries_table} WHERE map_id = %d ORDER BY display_order",
                $map_id
            )
        );
    }
    
    /**
     * AJAX: Create map
     */
    public function ajax_create_map() {
        check_ajax_referer('dakoii_maps_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $config = $_POST['config'] ?? array();
        $map_id = $this->create_map($config);
        
        if ($map_id) {
            wp_send_json_success(array(
                'map_id' => $map_id,
                'message' => __('Map created successfully', 'dakoii-maps')
            ));
        } else {
            wp_send_json_error(__('Failed to create map', 'dakoii-maps'));
        }
    }
    
    /**
     * AJAX: Update map
     */
    public function ajax_update_map() {
        check_ajax_referer('dakoii_maps_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $map_id = intval($_POST['map_id'] ?? 0);
        $config = $_POST['config'] ?? array();
        
        if ($this->update_map($map_id, $config)) {
            wp_send_json_success(__('Map updated successfully', 'dakoii-maps'));
        } else {
            wp_send_json_error(__('Failed to update map', 'dakoii-maps'));
        }
    }
    
    /**
     * AJAX: Delete map
     */
    public function ajax_delete_map() {
        check_ajax_referer('dakoii_maps_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $map_id = intval($_POST['map_id'] ?? 0);
        
        if ($this->delete_map($map_id)) {
            wp_send_json_success(__('Map deleted successfully', 'dakoii-maps'));
        } else {
            wp_send_json_error(__('Failed to delete map', 'dakoii-maps'));
        }
    }
    
    /**
     * AJAX: Get map
     */
    public function ajax_get_map() {
        check_ajax_referer('dakoii_maps_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $map_id = intval($_POST['map_id'] ?? 0);
        $map = $this->get_map($map_id);
        
        if ($map) {
            wp_send_json_success($map);
        } else {
            wp_send_json_error(__('Map not found', 'dakoii-maps'));
        }
    }
}
