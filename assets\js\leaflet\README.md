# Leaflet.js Library

This directory should contain the Leaflet.js library files.

## Required Files

Download Leaflet.js version 1.9.4 from https://leafletjs.com/download.html and place the following files here:

- `leaflet.js` - Main Leaflet JavaScript library
- `leaflet.css` - Leaflet CSS styles
- `images/` - Directory containing Leaflet image assets

## Installation Instructions

1. Go to https://leafletjs.com/download.html
2. Download Leaflet 1.9.4 (stable version)
3. Extract the downloaded zip file
4. Copy the following files to this directory:
   - `leaflet.js` → `assets/js/leaflet/leaflet.js`
   - `leaflet.css` → `assets/js/leaflet/leaflet.css`
   - `images/` folder → `assets/js/leaflet/images/`

## Alternative: CDN Usage

If you prefer to use CDN instead of local files, you can modify the plugin to load Leaflet from CDN by updating the enqueue scripts in the main plugin file.

CDN URLs for Leaflet 1.9.4:
- CSS: https://unpkg.com/leaflet@1.9.4/dist/leaflet.css
- JS: https://unpkg.com/leaflet@1.9.4/dist/leaflet.js

## License

Leaflet is open-source and released under the BSD 2-Clause License.
