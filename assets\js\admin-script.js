/**
 * Dakoii Maps Admin JavaScript
 */

(function($) {
    'use strict';
    
    // Admin object
    var DakoiiMapsAdmin = {
        
        /**
         * Initialize admin functionality
         */
        init: function() {
            this.bindEvents();
            this.initMapForm();
            this.initMapList();
        },
        
        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Map level selection
            $(document).on('change', 'input[name="map_level"]', this.handleMapLevelChange);

            // Boundary selection (dropdown)
            $(document).on('change', '.boundary-selector', this.handleBoundarySelection);

            // Parent boundary filters
            $(document).on('change', '.parent-boundary-filter', this.handleParentFilterChange);

            // Select all/none buttons
            $(document).on('click', '.select-all-boundaries', this.selectAllBoundaries);
            $(document).on('click', '.select-none-boundaries', this.selectNoneBoundaries);

            // Color picker changes
            $(document).on('change', 'input[type="color"]', this.handleColorChange);

            // Form submission
            $(document).on('submit', '#dakoii-map-form', this.handleFormSubmit);

            // Delete map confirmation
            $(document).on('click', '.delete-map', this.confirmDelete);

            // Copy shortcode
            $(document).on('click', '.copy-shortcode', this.copyShortcode);

            // Preview map
            $(document).on('click', '.preview-map', this.previewMap);

            // Clear cache
            $(document).on('click', '.clear-cache', this.clearCache);
        },
        
        /**
         * Initialize map form
         */
        initMapForm: function() {
            if ($('#dakoii-map-form').length === 0) {
                return;
            }
            
            // Initialize color pickers
            this.initColorPickers();
            
            // Load initial boundary options
            this.loadBoundaryOptions();
            
            // Initialize sortable boundaries
            this.initSortableBoundaries();
        },
        
        /**
         * Initialize map list
         */
        initMapList: function() {
            if ($('.dakoii-maps-table').length === 0) {
                return;
            }
            
            // Initialize bulk actions
            this.initBulkActions();
        },
        
        /**
         * Handle map level change
         */
        handleMapLevelChange: function() {
            var selectedLevel = $('input[name="map_level"]:checked').val();

            // Update UI based on selected level
            DakoiiMapsAdmin.updateMapLevelUI(selectedLevel);

            // Load boundary options for selected level
            DakoiiMapsAdmin.loadBoundaryOptions(selectedLevel);
        },

        /**
         * Update map level UI
         */
        updateMapLevelUI: function(level) {
            // Update level-specific help text
            $('.level-help').hide();
            $('.level-help[data-level="' + level + '"]').show();

            // Show/hide appropriate boundary level sections
            $('.boundary-level-section').hide();
            $('.boundary-level-section[data-level="' + level + '"]').show();

            // Update boundary selection section
            $('.boundary-selection').attr('data-level', level);

            // Clear previous selections
            $('.boundary-selector').each(function() {
                $(this).find('option').prop('selected', false);
            });
            DakoiiMapsAdmin.updateSelectedCount();

            // Load parent filters if needed
            if (level === 'district' || level === 'llg') {
                DakoiiMapsAdmin.loadParentFilters(level);
            }
        },
        
        /**
         * Load boundary options
         */
        loadBoundaryOptions: function(level, parentCodes) {
            if (!level) {
                level = $('input[name="map_level"]:checked').val();
            }

            if (!level) {
                return;
            }

            // Get the appropriate container based on level
            var $container = $('.' + level + '-options');
            if ($container.length === 0) {
                $container = $('.boundary-options');
            }

            $container.html('<div class="loading">Loading ' + level + ' boundaries...</div>');

            var data = {
                action: 'dakoii_get_boundary_options',
                type: level,
                parent_codes: parentCodes || [],
                nonce: dakoiiMapsAdmin.nonce
            };

            $.post(dakoiiMapsAdmin.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        DakoiiMapsAdmin.renderBoundaryOptions(response.data, level, $container);
                    } else {
                        $container.html('<div class="error">Failed to load ' + level + ' boundaries: ' + (response.data || 'Unknown error') + '</div>');
                    }
                })
                .fail(function() {
                    $container.html('<div class="error">Network error loading ' + level + ' boundaries</div>');
                });
        },
        
        /**
         * Render boundary options
         */
        renderBoundaryOptions: function(boundaries, level, $container) {
            // Get the appropriate selector element
            var $selector = $('#' + level + '-selector');

            if ($selector.length === 0) {
                console.error('Boundary selector not found for level: ' + level);
                return;
            }

            // Clear existing options
            $selector.empty();

            if (boundaries.length === 0) {
                $selector.append('<option value="" disabled>No ' + level + ' boundaries available</option>');
            } else {
                // Sort boundaries alphabetically by name
                boundaries.sort(function(a, b) {
                    return a.name.localeCompare(b.name);
                });

                // Add options to the select element
                boundaries.forEach(function(boundary) {
                    var option = $('<option></option>')
                        .attr('value', JSON.stringify(boundary))
                        .attr('data-boundary-type', boundary.type)
                        .attr('data-boundary-id', boundary.id)
                        .attr('data-boundary-geocode', boundary.geocode)
                        .text(boundary.name + ' (' + boundary.geocode + ')');

                    $selector.append(option);
                });
            }

            // Update the boundary type label
            var typeLabels = {
                'provincial': 'provinces',
                'district': 'districts',
                'llg': 'LLGs'
            };
            $('.boundary-type-label').text(typeLabels[level] || 'boundaries');

            // Update selected count
            DakoiiMapsAdmin.updateSelectedCount();
        },
        
        /**
         * Handle boundary selection
         */
        handleBoundarySelection: function() {
            DakoiiMapsAdmin.updateSelectedCount();
            DakoiiMapsAdmin.updateHiddenBoundaryInputs();

            // Update preview if available
            DakoiiMapsAdmin.updatePreview();
        },

        /**
         * Update selected count
         */
        updateSelectedCount: function() {
            var selectedCount = 0;
            $('.boundary-selector').each(function() {
                selectedCount += $(this).val() ? $(this).val().length : 0;
            });
            $('.selected-count').text(selectedCount);
        },

        /**
         * Update hidden boundary inputs for form submission
         */
        updateHiddenBoundaryInputs: function() {
            var $container = $('#selected-boundaries-container');
            $container.empty();

            $('.boundary-selector').each(function() {
                var $selector = $(this);
                var selectedValues = $selector.val();

                if (selectedValues && selectedValues.length > 0) {
                    selectedValues.forEach(function(value, index) {
                        var $input = $('<input>')
                            .attr('type', 'hidden')
                            .attr('name', 'selected_boundaries[]')
                            .attr('value', value);

                        $container.append($input);
                    });
                }
            });
        },

        /**
         * Load parent filters
         */
        loadParentFilters: function(level) {
            if (level === 'district') {
                // Load provinces for district filter
                DakoiiMapsAdmin.loadFilterOptions('provincial', '#province-filter');
            } else if (level === 'llg') {
                // Load provinces and districts for LLG filters
                DakoiiMapsAdmin.loadFilterOptions('provincial', '#province-filter-llg');
                DakoiiMapsAdmin.loadFilterOptions('district', '#district-filter-llg');
            }
        },

        /**
         * Load filter options
         */
        loadFilterOptions: function(type, selector) {
            var data = {
                action: 'dakoii_get_boundary_options',
                type: type,
                nonce: dakoiiMapsAdmin.nonce
            };

            $.post(dakoiiMapsAdmin.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        var $select = $(selector);
                        var currentValue = $select.val();

                        // Clear existing options except the first one
                        $select.find('option:not(:first)').remove();

                        // Sort and add new options
                        response.data.sort(function(a, b) {
                            return a.name.localeCompare(b.name);
                        });

                        response.data.forEach(function(boundary) {
                            $select.append('<option value="' + boundary.geocode + '">' + boundary.name + '</option>');
                        });

                        // Restore previous value if it still exists
                        if (currentValue) {
                            $select.val(currentValue);
                        }
                    }
                })
                .fail(function() {
                    console.error('Failed to load filter options for ' + type);
                });
        },

        /**
         * Handle parent filter change
         */
        handleParentFilterChange: function() {
            var $filter = $(this);
            var childLevel = $filter.data('child-level');
            var filterType = $filter.data('filter-type') || 'province';
            var selectedValue = $filter.val();

            // Get parent codes for filtering
            var parentCodes = [];
            if (selectedValue) {
                parentCodes.push(selectedValue);
            }

            // Load filtered boundaries
            DakoiiMapsAdmin.loadBoundaryOptions(childLevel, parentCodes);
        },

        /**
         * Select all boundaries
         */
        selectAllBoundaries: function() {
            var level = $(this).data('level');
            var $selector = $('#' + level + '-selector');
            $selector.find('option:not(:disabled)').prop('selected', true);
            DakoiiMapsAdmin.updateSelectedCount();
            DakoiiMapsAdmin.updateHiddenBoundaryInputs();
        },

        /**
         * Select no boundaries
         */
        selectNoneBoundaries: function() {
            var level = $(this).data('level');
            var $selector = $('#' + level + '-selector');
            $selector.find('option').prop('selected', false);
            DakoiiMapsAdmin.updateSelectedCount();
            DakoiiMapsAdmin.updateHiddenBoundaryInputs();
        },
        
        /**
         * Initialize color pickers
         */
        initColorPickers: function() {
            $('.color-picker-wrapper input[type="color"]').each(function() {
                var $colorInput = $(this);
                var $textInput = $colorInput.siblings('input[type="text"]');
                
                // Sync color picker with text input
                $colorInput.on('change', function() {
                    $textInput.val($colorInput.val());
                });
                
                $textInput.on('change', function() {
                    var color = $textInput.val();
                    if (/^#[0-9A-F]{6}$/i.test(color)) {
                        $colorInput.val(color);
                    }
                });
            });
        },
        
        /**
         * Handle color change
         */
        handleColorChange: function() {
            var $input = $(this);
            var $textInput = $input.siblings('input[type="text"]');
            $textInput.val($input.val());
            
            // Update preview if available
            DakoiiMapsAdmin.updatePreview();
        },
        
        /**
         * Initialize sortable boundaries
         */
        initSortableBoundaries: function() {
            if ($.fn.sortable) {
                $('.boundary-options').sortable({
                    items: '.boundary-option',
                    cursor: 'move',
                    opacity: 0.7,
                    update: function() {
                        DakoiiMapsAdmin.updatePreview();
                    }
                });
            }
        },
        
        /**
         * Handle form submit
         */
        handleFormSubmit: function(e) {
            var $form = $(this);
            var $submitButton = $form.find('input[type="submit"]');
            
            // Validate form
            if (!DakoiiMapsAdmin.validateForm($form)) {
                e.preventDefault();
                return false;
            }
            
            // Show loading state
            $submitButton.prop('disabled', true).val('Saving...');
            
            // Form will submit normally
            return true;
        },
        
        /**
         * Validate form
         */
        validateForm: function($form) {
            var isValid = true;
            var errors = [];
            
            // Check required fields
            var mapName = $form.find('input[name="map_name"]').val().trim();
            if (!mapName) {
                errors.push('Map name is required');
                isValid = false;
            }
            
            var mapLevel = $form.find('input[name="map_level"]:checked').val();
            if (!mapLevel) {
                errors.push('Map level is required');
                isValid = false;
            }
            
            var selectedBoundaries = 0;
            $('.boundary-selector').each(function() {
                selectedBoundaries += $(this).val() ? $(this).val().length : 0;
            });

            if (selectedBoundaries === 0) {
                errors.push('At least one boundary must be selected');
                isValid = false;
            }
            
            // Show errors
            if (!isValid) {
                alert(dakoiiMapsAdmin.strings.invalidConfig + '\n\n' + errors.join('\n'));
            }
            
            return isValid;
        },
        
        /**
         * Confirm delete
         */
        confirmDelete: function(e) {
            if (!confirm(dakoiiMapsAdmin.strings.confirmDelete)) {
                e.preventDefault();
                return false;
            }
        },
        
        /**
         * Copy shortcode to clipboard
         */
        copyShortcode: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var shortcode = $button.data('shortcode');
            
            // Create temporary input
            var $temp = $('<input>');
            $('body').append($temp);
            $temp.val(shortcode).select();
            
            try {
                document.execCommand('copy');
                $button.text('Copied!');
                setTimeout(function() {
                    $button.text('Copy');
                }, 2000);
            } catch (err) {
                alert('Failed to copy shortcode. Please copy manually: ' + shortcode);
            }
            
            $temp.remove();
        },
        
        /**
         * Preview map
         */
        previewMap: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var $form = $button.closest('form');
            
            // Collect form data
            var formData = DakoiiMapsAdmin.collectFormData($form);
            
            // Validate basic requirements
            if (!formData.map_name || !formData.map_level) {
                alert('Please fill in map name and select map level before previewing');
                return;
            }
            
            // Show loading
            $button.prop('disabled', true).text('Generating Preview...');
            
            var data = {
                action: 'dakoii_preview_map',
                config: formData,
                nonce: dakoiiMapsAdmin.nonce
            };
            
            $.post(dakoiiMapsAdmin.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        // Open preview in new window/tab
                        window.open(response.data.preview_url, '_blank');
                    } else {
                        alert('Failed to generate preview: ' + (response.data || 'Unknown error'));
                    }
                })
                .fail(function() {
                    alert('Network error while generating preview');
                })
                .always(function() {
                    $button.prop('disabled', false).text('Preview Map');
                });
        },
        
        /**
         * Clear cache
         */
        clearCache: function(e) {
            e.preventDefault();
            
            var $button = $(this);
            var cacheType = $button.data('cache-type') || 'all';
            
            $button.prop('disabled', true).text('Clearing...');
            
            var data = {
                action: 'dakoii_clear_cache',
                cache_type: cacheType,
                nonce: dakoiiMapsAdmin.nonce
            };
            
            $.post(dakoiiMapsAdmin.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        alert(response.data);
                    } else {
                        alert('Failed to clear cache: ' + (response.data || 'Unknown error'));
                    }
                })
                .fail(function() {
                    alert('Network error while clearing cache');
                })
                .always(function() {
                    $button.prop('disabled', false).text('Clear Cache');
                });
        },
        
        /**
         * Initialize bulk actions
         */
        initBulkActions: function() {
            // Select all checkbox
            $('#cb-select-all-1, #cb-select-all-2').on('change', function() {
                var checked = $(this).prop('checked');
                $('.wp-list-table tbody input[type="checkbox"]').prop('checked', checked);
            });
            
            // Individual checkboxes
            $('.wp-list-table tbody input[type="checkbox"]').on('change', function() {
                var totalCheckboxes = $('.wp-list-table tbody input[type="checkbox"]').length;
                var checkedCheckboxes = $('.wp-list-table tbody input[type="checkbox"]:checked').length;
                
                $('#cb-select-all-1, #cb-select-all-2').prop('checked', totalCheckboxes === checkedCheckboxes);
            });
        },
        
        /**
         * Collect form data
         */
        collectFormData: function($form) {
            var data = {};

            $form.find('input, select, textarea').each(function() {
                var $field = $(this);
                var name = $field.attr('name');
                var type = $field.attr('type');

                if (!name) return;

                // Skip boundary selectors - handle them separately
                if ($field.hasClass('boundary-selector')) {
                    return;
                }

                if (type === 'checkbox' || type === 'radio') {
                    if ($field.is(':checked')) {
                        if (name.endsWith('[]')) {
                            if (!data[name]) data[name] = [];
                            data[name].push($field.val());
                        } else {
                            data[name] = $field.val();
                        }
                    }
                } else {
                    data[name] = $field.val();
                }
            });

            // Collect selected boundaries from dropdowns
            data.selected_boundaries = [];
            $('.boundary-selector').each(function() {
                var $selector = $(this);
                var selectedValues = $selector.val();

                if (selectedValues && selectedValues.length > 0) {
                    selectedValues.forEach(function(value) {
                        try {
                            var boundary = JSON.parse(value);
                            data.selected_boundaries.push(boundary);
                        } catch (e) {
                            console.error('Error parsing boundary data:', e);
                        }
                    });
                }
            });

            return data;
        },
        
        /**
         * Update preview
         */
        updatePreview: function() {
            // Placeholder for preview update functionality
            // This would update a live preview if implemented
        },
        
        /**
         * Escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Initialize when document is ready
    $(document).ready(function() {
        DakoiiMapsAdmin.init();
    });
    
})(jQuery);
