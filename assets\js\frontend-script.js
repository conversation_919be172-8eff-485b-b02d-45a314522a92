/**
 * Dakoii Maps Frontend JavaScript
 */

(function($) {
    'use strict';
    
    /**
     * Map Display Class
     */
    window.DakoiiMapDisplay = function(containerId, config) {
        this.containerId = containerId;
        this.config = config;
        this.map = null;
        this.currentLayer = null;
        this.currentLevel = config.config.map_level;
        this.breadcrumb = [];
        
        this.init();
    };
    
    DakoiiMapDisplay.prototype = {
        
        /**
         * Initialize the map
         */
        init: function() {
            var self = this;
            
            // Wait for Leaflet to be available
            if (typeof L === 'undefined') {
                setTimeout(function() {
                    self.init();
                }, 100);
                return;
            }
            
            this.createMap();
            this.loadMapData();
            this.setupEventHandlers();
        },
        
        /**
         * Create Leaflet map instance
         */
        createMap: function() {
            var container = document.getElementById(this.containerId);
            if (!container) {
                console.error('Map container not found:', this.containerId);
                return;
            }
            
            var displayOptions = this.config.config.display_options;
            
            this.map = L.map(this.containerId, {
                center: [displayOptions.center.lat, displayOptions.center.lng],
                zoom: displayOptions.zoom.initial,
                minZoom: displayOptions.zoom.min,
                maxZoom: displayOptions.zoom.max,
                zoomControl: true,
                attributionControl: true
            });
            
            // Add base tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(this.map);
            
            // Hide loading indicator
            this.hideLoading();
        },
        
        /**
         * Load map data via AJAX
         */
        loadMapData: function() {
            var self = this;
            
            var data = {
                action: 'dakoii_get_map_data',
                map_id: this.config.map_id,
                nonce: dakoiiMaps.nonce
            };
            
            $.post(dakoiiMaps.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        self.displayBoundaries(response.data.boundaries);
                    } else {
                        self.showError(response.data || 'Failed to load map data');
                    }
                })
                .fail(function() {
                    self.showError('Network error loading map data');
                });
        },
        
        /**
         * Display boundaries on map
         */
        displayBoundaries: function(boundaryData) {
            if (!boundaryData || !boundaryData.features) {
                this.showError('No boundary data available');
                return;
            }
            
            // Remove existing layer
            if (this.currentLayer) {
                this.map.removeLayer(this.currentLayer);
            }
            
            var self = this;
            
            this.currentLayer = L.geoJSON(boundaryData, {
                style: function(feature) {
                    return self.getBoundaryStyle(feature);
                },
                onEachFeature: function(feature, layer) {
                    self.onEachFeature(feature, layer);
                }
            }).addTo(this.map);
            
            // Fit map to boundaries
            if (this.currentLayer.getBounds().isValid()) {
                this.map.fitBounds(this.currentLayer.getBounds(), {
                    padding: [10, 10]
                });
            }
            
            // Update legend if enabled
            if (this.config.config.features.legend_enabled) {
                this.updateLegend();
            }
        },
        
        /**
         * Get styling for boundaries
         */
        getBoundaryStyle: function(feature) {
            var styling = this.config.config.styling;
            
            return {
                color: styling.boundary_color,
                weight: styling.boundary_width,
                fillColor: styling.fill_color,
                fillOpacity: styling.fill_opacity,
                opacity: 1
            };
        },
        
        /**
         * Handle feature interactions
         */
        onEachFeature: function(feature, layer) {
            var self = this;
            
            // Add popup if enabled
            if (this.config.config.features.popup_enabled) {
                var popupContent = this.createPopupContent(feature);
                layer.bindPopup(popupContent);
            }
            
            // Add click handler for navigation
            if (this.config.config.features.click_navigation) {
                layer.on('click', function(e) {
                    self.handleBoundaryClick(feature, e);
                });
            }
            
            // Add hover effects
            layer.on('mouseover', function(e) {
                var styling = self.config.config.styling;
                layer.setStyle({
                    color: styling.hover_color,
                    fillOpacity: styling.hover_opacity,
                    weight: styling.boundary_width + 1
                });
                
                if (!L.Browser.ie && !L.Browser.opera && !L.Browser.edge) {
                    layer.bringToFront();
                }
            });
            
            layer.on('mouseout', function(e) {
                self.currentLayer.resetStyle(layer);
            });
        },
        
        /**
         * Create popup content for feature
         */
        createPopupContent: function(feature) {
            var props = feature.properties;
            var content = '<div class="dakoii-popup">';
            
            // Add title based on boundary type
            if (props.PROVNAME) {
                content += '<h4>' + this.escapeHtml(props.PROVNAME) + '</h4>';
                content += '<p><strong>Type:</strong> Province</p>';
            } else if (props.DISTNAME) {
                content += '<h4>' + this.escapeHtml(props.DISTNAME) + '</h4>';
                content += '<p><strong>Type:</strong> District</p>';
            } else if (props.LLGNAME) {
                content += '<h4>' + this.escapeHtml(props.LLGNAME) + '</h4>';
                content += '<p><strong>Type:</strong> Local Level Government</p>';
            }
            
            // Add geocode if available
            if (props.GEOCODE || props.PROVID) {
                var code = props.GEOCODE || props.PROVID;
                content += '<p><strong>Code:</strong> ' + this.escapeHtml(code) + '</p>';
            }
            
            // Add FID if available
            if (props.FID) {
                content += '<p><strong>ID:</strong> ' + this.escapeHtml(props.FID) + '</p>';
            }
            
            // Add navigation action if applicable
            if (this.config.config.features.click_navigation && this.canNavigateDeeper()) {
                content += '<div class="popup-actions">';
                content += '<a href="#" class="popup-action" onclick="return false;">Click to zoom in</a>';
                content += '</div>';
            }
            
            content += '</div>';
            return content;
        },
        
        /**
         * Handle boundary click for navigation
         */
        handleBoundaryClick: function(feature, event) {
            if (!this.canNavigateDeeper()) {
                return;
            }
            
            var geocode = feature.properties.GEOCODE || feature.properties.PROVID;
            var name = this.getBoundaryName(feature);
            
            if (geocode && name) {
                this.navigateToChild(geocode, name);
            }
        },
        
        /**
         * Check if we can navigate deeper
         */
        canNavigateDeeper: function() {
            var levelHierarchy = ['provincial', 'district', 'llg'];
            var currentIndex = levelHierarchy.indexOf(this.currentLevel);
            return currentIndex < levelHierarchy.length - 1;
        },
        
        /**
         * Get child level for current level
         */
        getChildLevel: function(currentLevel) {
            var hierarchy = {
                'provincial': 'district',
                'district': 'llg'
            };
            
            return hierarchy[currentLevel] || null;
        },
        
        /**
         * Navigate to child level
         */
        navigateToChild: function(parentGeocode, parentName) {
            var childLevel = this.getChildLevel(this.currentLevel);
            
            if (!childLevel) {
                return;
            }
            
            var self = this;
            
            // Show loading
            this.showLoading();
            
            var data = {
                action: 'dakoii_get_child_boundaries',
                parent_geocode: parentGeocode,
                child_level: childLevel,
                nonce: dakoiiMaps.nonce
            };
            
            $.post(dakoiiMaps.ajaxUrl, data)
                .done(function(response) {
                    if (response.success) {
                        // Update breadcrumb
                        self.breadcrumb.push({
                            level: self.currentLevel,
                            geocode: parentGeocode,
                            name: parentName
                        });
                        
                        // Update current level
                        self.currentLevel = childLevel;
                        
                        // Display new boundaries
                        self.displayBoundaries(response.data);
                        
                        // Update breadcrumb display
                        self.updateBreadcrumb();
                    } else {
                        self.showError(response.data || 'Failed to load child boundaries');
                    }
                })
                .fail(function() {
                    self.showError('Network error loading child boundaries');
                })
                .always(function() {
                    self.hideLoading();
                });
        },
        
        /**
         * Navigate back to parent level
         */
        navigateBack: function(targetIndex) {
            if (this.breadcrumb.length === 0) {
                return;
            }
            
            // Remove breadcrumb items after target index
            if (typeof targetIndex !== 'undefined') {
                this.breadcrumb = this.breadcrumb.slice(0, targetIndex + 1);
            } else {
                this.breadcrumb.pop();
            }
            
            // Reset to original map if no breadcrumb
            if (this.breadcrumb.length === 0) {
                this.currentLevel = this.config.config.map_level;
                this.loadMapData();
            } else {
                // Navigate to the last breadcrumb item
                var lastItem = this.breadcrumb[this.breadcrumb.length - 1];
                this.navigateToChild(lastItem.geocode, lastItem.name);
            }
            
            this.updateBreadcrumb();
        },
        
        /**
         * Reset to original view
         */
        resetView: function() {
            this.breadcrumb = [];
            this.currentLevel = this.config.config.map_level;
            this.loadMapData();
            this.updateBreadcrumb();
        },
        
        /**
         * Update breadcrumb display
         */
        updateBreadcrumb: function() {
            var $breadcrumb = $('.dakoii-map-breadcrumb');
            
            if (this.breadcrumb.length === 0) {
                $breadcrumb.hide();
                return;
            }
            
            var $path = $breadcrumb.find('.breadcrumb-path');
            var pathHtml = '';
            
            for (var i = 0; i < this.breadcrumb.length; i++) {
                if (i > 0) {
                    pathHtml += ' <span class="breadcrumb-separator">></span> ';
                }
                
                pathHtml += '<a href="#" data-index="' + i + '">' + 
                           this.escapeHtml(this.breadcrumb[i].name) + '</a>';
            }
            
            $path.html(pathHtml);
            $breadcrumb.show();
            
            // Bind breadcrumb click events
            var self = this;
            $path.find('a').on('click', function(e) {
                e.preventDefault();
                var index = parseInt($(this).data('index'));
                self.navigateBack(index);
            });
        },
        
        /**
         * Get boundary name from feature
         */
        getBoundaryName: function(feature) {
            var props = feature.properties;
            return props.PROVNAME || props.DISTNAME || props.LLGNAME || '';
        },
        
        /**
         * Update legend
         */
        updateLegend: function() {
            var $legend = $('.dakoii-map-legend');
            if ($legend.length === 0) {
                return;
            }
            
            var styling = this.config.config.styling;
            var levelName = this.currentLevel.charAt(0).toUpperCase() + this.currentLevel.slice(1);
            
            var legendHtml = '<div class="legend-items">';
            legendHtml += '<div class="legend-item">';
            legendHtml += '<div class="legend-color" style="background-color: ' + styling.fill_color + '; border-color: ' + styling.boundary_color + ';"></div>';
            legendHtml += '<span>' + levelName + ' Boundaries</span>';
            legendHtml += '</div>';
            legendHtml += '</div>';
            
            $legend.find('.legend-items').remove();
            $legend.append(legendHtml);
        },
        
        /**
         * Setup event handlers
         */
        setupEventHandlers: function() {
            var self = this;
            
            // Reset view button
            $('.breadcrumb-reset').on('click', function(e) {
                e.preventDefault();
                self.resetView();
            });
            
            // Window resize handler
            $(window).on('resize', function() {
                if (self.map) {
                    self.map.invalidateSize();
                }
            });
        },
        
        /**
         * Show loading indicator
         */
        showLoading: function() {
            $('#' + this.containerId + ' .dakoii-map-loading').show();
        },
        
        /**
         * Hide loading indicator
         */
        hideLoading: function() {
            $('#' + this.containerId + ' .dakoii-map-loading').hide();
        },
        
        /**
         * Show error message
         */
        showError: function(message) {
            this.hideLoading();
            
            var errorHtml = '<div class="dakoii-map-error">';
            errorHtml += '<p>' + this.escapeHtml(message) + '</p>';
            errorHtml += '</div>';
            
            $('#' + this.containerId).html(errorHtml);
        },
        
        /**
         * Escape HTML
         */
        escapeHtml: function(text) {
            var map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            
            return String(text).replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    };
    
    // Auto-initialize maps when DOM is ready
    $(document).ready(function() {
        $('.dakoii-map-container[data-map-config]').each(function() {
            var $container = $(this);
            var containerId = $container.attr('id');
            var config = $container.data('map-config');
            
            if (containerId && config) {
                new DakoiiMapDisplay(containerId, config);
            }
        });
    });
    
})(jQuery);
