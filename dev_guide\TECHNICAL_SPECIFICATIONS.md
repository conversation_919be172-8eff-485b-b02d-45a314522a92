# Dakoii Prov - Maps Plugin Technical Specifications

## Overview

This document provides detailed technical specifications for implementing the Dakoii Prov - Maps Plugin, including code examples, API definitions, and integration guidelines.

## Plugin Architecture

### File Structure
```
dakoii-prov-map-boundries/
├── dakoii-prov-maps.php              # Main plugin file
├── uninstall.php                     # Cleanup on uninstall
├── readme.txt                        # WordPress plugin readme
├── assets/                           # Static assets
│   ├── css/
│   │   ├── admin-style.css          # Admin interface styles
│   │   └── frontend-style.css       # Frontend map styles
│   ├── js/
│   │   ├── admin-script.js          # Admin functionality
│   │   ├── frontend-script.js       # Frontend map interactions
│   │   └── leaflet/                 # Leaflet.js library files
│   ├── images/                      # Plugin images and icons
│   ├── png_prov_boundaries_2011.json
│   ├── png_dist_boundaries_2011.json
│   └── png_llg_boundaries_2011.json
├── includes/                         # Core PHP classes
│   ├── class-map-manager.php
│   ├── class-boundary-loader.php
│   ├── class-shortcode-handler.php
│   ├── class-admin-interface.php
│   ├── class-rest-api.php
│   └── class-cache-manager.php
├── templates/                        # Template files
│   ├── admin/
│   │   ├── map-list.php
│   │   ├── map-create.php
│   │   ├── map-edit.php
│   │   └── settings.php
│   └── frontend/
│       ├── map-display.php
│       └── map-popup.php
└── languages/                        # Translation files
    ├── dakoii-maps.pot
    └── dakoii-maps-en_US.po
```

## Database Schema

### Primary Tables

#### wp_dakoii_maps
```sql
CREATE TABLE wp_dakoii_maps (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    map_level ENUM('provincial', 'district', 'llg') NOT NULL,
    config LONGTEXT NOT NULL COMMENT 'JSON configuration',
    shortcode VARCHAR(50) UNIQUE NOT NULL,
    status ENUM('active', 'inactive', 'draft') DEFAULT 'active',
    created_by INT(11) UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_shortcode (shortcode),
    INDEX idx_map_level (map_level),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### wp_dakoii_map_boundaries
```sql
CREATE TABLE wp_dakoii_map_boundaries (
    id INT(11) UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    map_id INT(11) UNSIGNED NOT NULL,
    boundary_type ENUM('province', 'district', 'llg') NOT NULL,
    boundary_id VARCHAR(50) NOT NULL,
    boundary_name VARCHAR(255) NOT NULL,
    geocode VARCHAR(50),
    display_order INT(11) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (map_id) REFERENCES wp_dakoii_maps(id) ON DELETE CASCADE,
    INDEX idx_map_id (map_id),
    INDEX idx_boundary_type (boundary_type),
    INDEX idx_geocode (geocode),
    INDEX idx_display_order (display_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Core Classes Specifications

### 1. DakoiiMapManager Class

```php
<?php
/**
 * Map Manager Class
 * Handles all map-related operations
 */
class DakoiiMapManager {
    
    /**
     * Create a new map
     * 
     * @param array $config Map configuration
     * @return int|false Map ID on success, false on failure
     */
    public function create_map($config) {
        // Validate configuration
        $validated_config = $this->validate_map_config($config);
        if (!$validated_config) {
            return false;
        }
        
        // Generate unique shortcode
        $shortcode = $this->generate_unique_shortcode();
        
        // Insert map record
        global $wpdb;
        $result = $wpdb->insert(
            $this->get_maps_table(),
            array(
                'name' => sanitize_text_field($config['name']),
                'description' => sanitize_textarea_field($config['description']),
                'map_level' => sanitize_text_field($config['map_level']),
                'config' => wp_json_encode($validated_config),
                'shortcode' => $shortcode,
                'created_by' => get_current_user_id()
            ),
            array('%s', '%s', '%s', '%s', '%s', '%d')
        );
        
        if ($result) {
            $map_id = $wpdb->insert_id;
            $this->save_map_boundaries($map_id, $config['boundaries']);
            do_action('dakoii_map_created', $map_id, $config);
            return $map_id;
        }
        
        return false;
    }
    
    /**
     * Validate map configuration
     * 
     * @param array $config Configuration to validate
     * @return array|false Validated config or false
     */
    private function validate_map_config($config) {
        $required_fields = array('name', 'map_level', 'boundaries');
        
        foreach ($required_fields as $field) {
            if (!isset($config[$field]) || empty($config[$field])) {
                return false;
            }
        }
        
        // Validate map level
        $valid_levels = array('provincial', 'district', 'llg');
        if (!in_array($config['map_level'], $valid_levels)) {
            return false;
        }
        
        // Set defaults for optional fields
        $defaults = array(
            'description' => '',
            'display_options' => $this->get_default_display_options(),
            'styling' => $this->get_default_styling(),
            'features' => $this->get_default_features()
        );
        
        return wp_parse_args($config, $defaults);
    }
    
    /**
     * Get default display options
     * 
     * @return array Default display configuration
     */
    private function get_default_display_options() {
        return array(
            'width' => '100%',
            'height' => '500px',
            'zoom' => array(
                'initial' => 6,
                'min' => 4,
                'max' => 12
            ),
            'center' => array(
                'lat' => -6.314993,
                'lng' => 143.95555
            )
        );
    }
}
```

### 2. DakoniiBoundaryLoader Class

```php
<?php
/**
 * Boundary Loader Class
 * Handles loading and filtering of boundary data
 */
class DakoniiBoundaryLoader {
    
    private $cache_group = 'dakoii_boundaries';
    private $cache_expiry = 3600; // 1 hour
    
    /**
     * Load boundaries by type with optional filtering
     * 
     * @param string $type Boundary type (provincial, district, llg)
     * @param array $filters Optional filters
     * @return array Boundary data
     */
    public function load_boundaries($type, $filters = array()) {
        $cache_key = $this->generate_cache_key($type, $filters);
        $boundaries = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $boundaries) {
            $boundaries = $this->load_boundaries_from_file($type);
            
            if (!empty($filters)) {
                $boundaries = $this->apply_filters($boundaries, $filters);
            }
            
            wp_cache_set($cache_key, $boundaries, $this->cache_group, $this->cache_expiry);
        }
        
        return $boundaries;
    }
    
    /**
     * Load boundaries from JSON file
     * 
     * @param string $type Boundary type
     * @return array Boundary data
     */
    private function load_boundaries_from_file($type) {
        $file_map = array(
            'provincial' => 'png_prov_boundaries_2011.json',
            'district' => 'png_dist_boundaries_2011.json',
            'llg' => 'png_llg_boundaries_2011.json'
        );
        
        if (!isset($file_map[$type])) {
            return array();
        }
        
        $file_path = DAKOII_MAPS_PLUGIN_DIR . 'assets/' . $file_map[$type];
        
        if (!file_exists($file_path)) {
            error_log("Dakoii Maps: Boundary file not found: {$file_path}");
            return array();
        }
        
        $json_content = file_get_contents($file_path);
        $data = json_decode($json_content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Dakoii Maps: JSON decode error: " . json_last_error_msg());
            return array();
        }
        
        return $data;
    }
    
    /**
     * Apply filters to boundary data
     * 
     * @param array $boundaries Boundary data
     * @param array $filters Filters to apply
     * @return array Filtered boundary data
     */
    private function apply_filters($boundaries, $filters) {
        if (!isset($boundaries['features']) || empty($filters)) {
            return $boundaries;
        }
        
        $filtered_features = array();
        
        foreach ($boundaries['features'] as $feature) {
            if ($this->feature_matches_filters($feature, $filters)) {
                $filtered_features[] = $feature;
            }
        }
        
        $boundaries['features'] = $filtered_features;
        return $boundaries;
    }
    
    /**
     * Check if feature matches filters
     * 
     * @param array $feature GeoJSON feature
     * @param array $filters Filters to check
     * @return bool True if matches
     */
    private function feature_matches_filters($feature, $filters) {
        if (!isset($feature['properties'])) {
            return false;
        }
        
        $properties = $feature['properties'];
        
        // Filter by geocode prefix
        if (isset($filters['geocode_prefix'])) {
            $geocode = isset($properties['GEOCODE']) ? $properties['GEOCODE'] : 
                      (isset($properties['PROVID']) ? $properties['PROVID'] : '');
            
            foreach ($filters['geocode_prefix'] as $prefix) {
                if (strpos($geocode, $prefix) === 0) {
                    return true;
                }
            }
            return false;
        }
        
        // Filter by specific IDs
        if (isset($filters['ids'])) {
            $id = isset($properties['FID']) ? $properties['FID'] : null;
            return in_array($id, $filters['ids']);
        }
        
        return true;
    }
}
```

## Frontend JavaScript Specifications

### Map Initialization

```javascript
/**
 * Dakoii Maps Frontend JavaScript
 */
class DakoiiMapDisplay {
    
    constructor(containerId, config) {
        this.containerId = containerId;
        this.config = config;
        this.map = null;
        this.currentLayer = null;
        this.init();
    }
    
    /**
     * Initialize the map
     */
    init() {
        this.createMap();
        this.loadBoundaries();
        this.setupEventHandlers();
    }
    
    /**
     * Create Leaflet map instance
     */
    createMap() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            console.error('Map container not found:', this.containerId);
            return;
        }
        
        this.map = L.map(this.containerId, {
            center: [this.config.center.lat, this.config.center.lng],
            zoom: this.config.zoom.initial,
            minZoom: this.config.zoom.min,
            maxZoom: this.config.zoom.max
        });
        
        // Add base tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(this.map);
    }
    
    /**
     * Load boundary data via AJAX
     */
    loadBoundaries() {
        const data = {
            action: 'dakoii_get_map_data',
            map_id: this.config.map_id,
            nonce: dakoiiMaps.nonce
        };
        
        jQuery.post(dakoiiMaps.ajaxUrl, data)
            .done((response) => {
                if (response.success) {
                    this.displayBoundaries(response.data);
                } else {
                    this.showError('Failed to load map data');
                }
            })
            .fail(() => {
                this.showError('Network error loading map data');
            });
    }
    
    /**
     * Display boundaries on map
     */
    displayBoundaries(boundaryData) {
        if (this.currentLayer) {
            this.map.removeLayer(this.currentLayer);
        }
        
        this.currentLayer = L.geoJSON(boundaryData, {
            style: this.getBoundaryStyle.bind(this),
            onEachFeature: this.onEachFeature.bind(this)
        }).addTo(this.map);
        
        // Fit map to boundaries
        this.map.fitBounds(this.currentLayer.getBounds());
    }
    
    /**
     * Get styling for boundaries
     */
    getBoundaryStyle(feature) {
        return {
            color: this.config.styling.boundary_color,
            weight: this.config.styling.boundary_width,
            fillColor: this.config.styling.fill_color,
            fillOpacity: this.config.styling.fill_opacity
        };
    }
    
    /**
     * Handle feature interactions
     */
    onEachFeature(feature, layer) {
        // Add popup if enabled
        if (this.config.features.popup_enabled) {
            const popupContent = this.createPopupContent(feature);
            layer.bindPopup(popupContent);
        }
        
        // Add click handler for navigation
        if (this.config.features.click_navigation) {
            layer.on('click', (e) => {
                this.handleBoundaryClick(feature, e);
            });
        }
        
        // Add hover effects
        layer.on('mouseover', (e) => {
            layer.setStyle({
                color: this.config.styling.hover_color,
                fillOpacity: this.config.styling.hover_opacity
            });
        });
        
        layer.on('mouseout', (e) => {
            this.currentLayer.resetStyle(layer);
        });
    }
    
    /**
     * Create popup content for feature
     */
    createPopupContent(feature) {
        const props = feature.properties;
        let content = '<div class="dakoii-popup">';
        
        if (props.PROVNAME) {
            content += `<h4>${props.PROVNAME}</h4>`;
        } else if (props.DISTNAME) {
            content += `<h4>${props.DISTNAME}</h4>`;
        } else if (props.LLGNAME) {
            content += `<h4>${props.LLGNAME}</h4>`;
        }
        
        if (props.GEOCODE || props.PROVID) {
            const code = props.GEOCODE || props.PROVID;
            content += `<p><strong>Code:</strong> ${code}</p>`;
        }
        
        content += '</div>';
        return content;
    }
    
    /**
     * Handle boundary click for navigation
     */
    handleBoundaryClick(feature, event) {
        // Implement hierarchical navigation logic
        const geocode = feature.properties.GEOCODE || feature.properties.PROVID;
        
        if (geocode && this.config.map_level !== 'llg') {
            this.navigateToChild(geocode);
        }
    }
    
    /**
     * Navigate to child level
     */
    navigateToChild(parentGeocode) {
        const childLevel = this.getChildLevel(this.config.map_level);
        
        if (!childLevel) return;
        
        const data = {
            action: 'dakoii_get_child_boundaries',
            parent_geocode: parentGeocode,
            child_level: childLevel,
            nonce: dakoiiMaps.nonce
        };
        
        jQuery.post(dakoiiMaps.ajaxUrl, data)
            .done((response) => {
                if (response.success) {
                    this.displayBoundaries(response.data);
                    this.updateBreadcrumb(parentGeocode);
                }
            });
    }
    
    /**
     * Get child level for current level
     */
    getChildLevel(currentLevel) {
        const hierarchy = {
            'provincial': 'district',
            'district': 'llg'
        };
        
        return hierarchy[currentLevel] || null;
    }
}

// Initialize maps when DOM is ready
jQuery(document).ready(function($) {
    $('.dakoii-map-container').each(function() {
        const containerId = $(this).attr('id');
        const config = $(this).data('config');
        
        new DakoiiMapDisplay(containerId, config);
    });
});
```

## REST API Endpoints

### Map Data Endpoint
```php
/**
 * REST API endpoint for map data
 */
register_rest_route('dakoii-maps/v1', '/maps/(?P<id>\d+)', array(
    'methods' => 'GET',
    'callback' => 'dakoii_get_map_data',
    'permission_callback' => '__return_true',
    'args' => array(
        'id' => array(
            'validate_callback' => function($param, $request, $key) {
                return is_numeric($param);
            }
        )
    )
));

function dakoii_get_map_data($request) {
    $map_id = $request['id'];
    $map_manager = new DakoiiMapManager();
    $map = $map_manager->get_map($map_id);
    
    if (!$map) {
        return new WP_Error('map_not_found', 'Map not found', array('status' => 404));
    }
    
    $boundary_loader = new DakoniiBoundaryLoader();
    $boundaries = $boundary_loader->load_boundaries($map->map_level, array(
        'map_id' => $map_id
    ));
    
    return rest_ensure_response(array(
        'map' => $map,
        'boundaries' => $boundaries
    ));
}
```

This technical specification provides the foundation for implementing the Dakoii Prov - Maps Plugin with proper architecture, security, and performance considerations.
