<?php
/**
 * Admin template: Settings
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap dakoii-maps-admin">
    <div class="dakoii-maps-header">
        <h1><?php _e('Dakoii Maps Settings', 'dakoii-maps'); ?></h1>
        <p><?php _e('Configure global settings for the Dakoii Maps plugin.', 'dakoii-maps'); ?></p>
    </div>

    <form method="post" action="options.php">
        <?php
        settings_fields('dakoii_maps_settings');
        do_settings_sections('dakoii_maps_settings');
        ?>

        <div class="dakoii-form-section">
            <h3><?php _e('General Settings', 'dakoii-maps'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Enable Caching', 'dakoii-maps'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="dakoii_maps_settings[cache_enabled]" value="1" 
                                   <?php checked(isset($settings['cache_enabled']) ? $settings['cache_enabled'] : true); ?>>
                            <?php _e('Enable caching for better performance', 'dakoii-maps'); ?>
                        </label>
                        <p class="description">
                            <?php _e('Caching improves performance by storing boundary data temporarily.', 'dakoii-maps'); ?>
                        </p>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('Cache Expiry Time', 'dakoii-maps'); ?></th>
                    <td>
                        <input type="number" name="dakoii_maps_settings[cache_expiry]" 
                               value="<?php echo esc_attr($settings['cache_expiry'] ?? 3600); ?>" 
                               min="300" max="86400" class="small-text">
                        <span><?php _e('seconds', 'dakoii-maps'); ?></span>
                        <p class="description">
                            <?php _e('How long to cache boundary data (300-86400 seconds).', 'dakoii-maps'); ?>
                        </p>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('Enable Debug Mode', 'dakoii-maps'); ?></th>
                    <td>
                        <label>
                            <input type="checkbox" name="dakoii_maps_settings[enable_debug]" value="1" 
                                   <?php checked(isset($settings['enable_debug']) ? $settings['enable_debug'] : false); ?>>
                            <?php _e('Enable debug logging', 'dakoii-maps'); ?>
                        </label>
                        <p class="description">
                            <?php _e('Enable detailed logging for troubleshooting (not recommended for production).', 'dakoii-maps'); ?>
                        </p>
                    </td>
                </tr>
            </table>
        </div>

        <div class="dakoii-form-section">
            <h3><?php _e('Default Map Settings', 'dakoii-maps'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Default Map Width', 'dakoii-maps'); ?></th>
                    <td>
                        <input type="text" name="dakoii_maps_settings[default_map_width]" 
                               value="<?php echo esc_attr($settings['default_map_width'] ?? '100%'); ?>" 
                               class="regular-text">
                        <p class="description">
                            <?php _e('Default width for new maps (e.g., 100%, 800px).', 'dakoii-maps'); ?>
                        </p>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('Default Map Height', 'dakoii-maps'); ?></th>
                    <td>
                        <input type="text" name="dakoii_maps_settings[default_map_height]" 
                               value="<?php echo esc_attr($settings['default_map_height'] ?? '500px'); ?>" 
                               class="regular-text">
                        <p class="description">
                            <?php _e('Default height for new maps (e.g., 500px, 400px).', 'dakoii-maps'); ?>
                        </p>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('Default Zoom Level', 'dakoii-maps'); ?></th>
                    <td>
                        <input type="number" name="dakoii_maps_settings[default_zoom_level]" 
                               value="<?php echo esc_attr($settings['default_zoom_level'] ?? 6); ?>" 
                               min="1" max="12" class="small-text">
                        <p class="description">
                            <?php _e('Default initial zoom level for new maps (1-12).', 'dakoii-maps'); ?>
                        </p>
                    </td>
                </tr>
            </table>
        </div>

        <div class="dakoii-form-section">
            <h3><?php _e('Cache Management', 'dakoii-maps'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Cache Status', 'dakoii-maps'); ?></th>
                    <td>
                        <p>
                            <strong><?php _e('Status:', 'dakoii-maps'); ?></strong>
                            <?php if ($cache_stats['enabled']): ?>
                                <span style="color: green;"><?php _e('Enabled', 'dakoii-maps'); ?></span>
                            <?php else: ?>
                                <span style="color: red;"><?php _e('Disabled', 'dakoii-maps'); ?></span>
                            <?php endif; ?>
                        </p>
                        
                        <div style="margin-top: 10px;">
                            <button type="button" class="button clear-cache" data-cache-type="all">
                                <?php _e('Clear All Cache', 'dakoii-maps'); ?>
                            </button>
                            <button type="button" class="button clear-cache" data-cache-type="boundaries">
                                <?php _e('Clear Boundary Cache', 'dakoii-maps'); ?>
                            </button>
                            <button type="button" class="button clear-cache" data-cache-type="maps">
                                <?php _e('Clear Map Cache', 'dakoii-maps'); ?>
                            </button>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div class="dakoii-form-section">
            <h3><?php _e('System Information', 'dakoii-maps'); ?></h3>
            
            <table class="form-table">
                <tr>
                    <th scope="row"><?php _e('Plugin Version', 'dakoii-maps'); ?></th>
                    <td><?php echo esc_html(DAKOII_MAPS_VERSION); ?></td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('WordPress Version', 'dakoii-maps'); ?></th>
                    <td><?php echo esc_html(get_bloginfo('version')); ?></td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('PHP Version', 'dakoii-maps'); ?></th>
                    <td><?php echo esc_html(PHP_VERSION); ?></td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('Boundary Files', 'dakoii-maps'); ?></th>
                    <td>
                        <?php
                        $boundary_files = array(
                            'Provincial' => 'png_prov_boundaries_2011.json',
                            'District' => 'png_dist_boundaries_2011.json',
                            'LLG' => 'png_llg_boundaries_2011.json'
                        );
                        
                        foreach ($boundary_files as $label => $filename) {
                            $file_path = DAKOII_MAPS_PLUGIN_DIR . 'assets/' . $filename;
                            $exists = file_exists($file_path);
                            $size = $exists ? size_format(filesize($file_path)) : 'N/A';
                            
                            echo '<p>';
                            echo '<strong>' . esc_html($label) . ':</strong> ';
                            if ($exists) {
                                echo '<span style="color: green;">✓ Found</span> (' . esc_html($size) . ')';
                            } else {
                                echo '<span style="color: red;">✗ Missing</span>';
                            }
                            echo '</p>';
                        }
                        ?>
                    </td>
                </tr>

                <tr>
                    <th scope="row"><?php _e('Database Tables', 'dakoii-maps'); ?></th>
                    <td>
                        <?php
                        global $wpdb;
                        $tables = array(
                            'Maps' => $wpdb->prefix . 'dakoii_maps',
                            'Boundaries' => $wpdb->prefix . 'dakoii_map_boundaries'
                        );
                        
                        foreach ($tables as $label => $table_name) {
                            $exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") === $table_name;
                            $count = $exists ? $wpdb->get_var("SELECT COUNT(*) FROM $table_name") : 0;
                            
                            echo '<p>';
                            echo '<strong>' . esc_html($label) . ':</strong> ';
                            if ($exists) {
                                echo '<span style="color: green;">✓ Exists</span> (' . esc_html($count) . ' records)';
                            } else {
                                echo '<span style="color: red;">✗ Missing</span>';
                            }
                            echo '</p>';
                        }
                        ?>
                    </td>
                </tr>
            </table>
        </div>

        <?php submit_button(); ?>
    </form>

    <div class="dakoii-form-section">
        <h3><?php _e('Support & Documentation', 'dakoii-maps'); ?></h3>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div style="padding: 20px; background: #f9f9f9; border-radius: 4px;">
                <h4><?php _e('Documentation', 'dakoii-maps'); ?></h4>
                <p><?php _e('Learn how to use the plugin effectively with our comprehensive documentation.', 'dakoii-maps'); ?></p>
                <a href="#" class="button" target="_blank"><?php _e('View Documentation', 'dakoii-maps'); ?></a>
            </div>

            <div style="padding: 20px; background: #f9f9f9; border-radius: 4px;">
                <h4><?php _e('Support', 'dakoii-maps'); ?></h4>
                <p><?php _e('Need help? Get support from our community or contact the developers.', 'dakoii-maps'); ?></p>
                <a href="#" class="button" target="_blank"><?php _e('Get Support', 'dakoii-maps'); ?></a>
            </div>

            <div style="padding: 20px; background: #f9f9f9; border-radius: 4px;">
                <h4><?php _e('Feature Requests', 'dakoii-maps'); ?></h4>
                <p><?php _e('Have an idea for a new feature? Let us know what you\'d like to see added.', 'dakoii-maps'); ?></p>
                <a href="#" class="button" target="_blank"><?php _e('Request Feature', 'dakoii-maps'); ?></a>
            </div>
        </div>
    </div>
</div>
