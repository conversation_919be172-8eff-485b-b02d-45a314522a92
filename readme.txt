=== Dakoii Provincial Map Boundries ===
Contributors: nolandgande
Tags: maps, papua new guinea, boundaries, interactive, leaflet, provincial, district, llg
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
Stable tag: 1.0.0
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Interactive maps for Papua New Guinea administrative boundaries with hierarchical navigation and shortcode support.

== Description ==

The Dakoii Prov - Maps Plugin allows you to create and display interactive maps of Papua New Guinea's administrative boundaries on your WordPress website. Create maps at three different levels: Provincial (22 provinces), District (87 districts), and Local Level Government (326 LLGs).

= Key Features =

* **Three Administrative Levels**: Provincial, District, and LLG boundaries
* **Hierarchical Navigation**: Click-through from provinces to districts to LLGs
* **Unique Shortcodes**: Each map gets its own shortcode for flexible placement
* **Interactive Maps**: Built on Leaflet.js with popups and hover effects
* **Customizable Styling**: Colors, zoom levels, dimensions, and more
* **Performance Optimized**: Caching system for fast loading
* **Mobile Responsive**: Works perfectly on all devices

= Use Cases =

* Government websites displaying administrative divisions
* Educational sites teaching about PNG geography
* Business websites showing service areas
* Tourism sites highlighting regions
* Research and data visualization projects

= Technical Features =

* WordPress-native admin interface
* Secure with nonce verification and capability checks
* Efficient caching system
* Clean, semantic code following WordPress standards
* Accessibility-friendly design
* SEO-optimized output

== Installation ==

1. Upload the plugin files to the `/wp-content/plugins/dakoii-prov-map-boundries` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the 'Plugins' screen in WordPress.
3. Use the Dakoii Maps menu item to configure the plugin and create your first map.

== Frequently Asked Questions ==

= How do I create a map? =

1. Go to Dakoii Maps → Add New Map in your WordPress admin
2. Enter a name and description for your map
3. Select the administrative level (Provincial, District, or LLG)
4. Choose which boundaries to include
5. Configure display options and styling
6. Save the map and copy the generated shortcode

= How do I display a map on my website? =

Use the shortcode generated when you create a map. For example: `[dakoii_map_abc123def]`

You can paste this shortcode into any post, page, or widget that supports shortcodes.

= Can I customize the map appearance? =

Yes! You can customize:
* Map dimensions (width and height)
* Zoom levels (initial, minimum, maximum)
* Colors (boundary, fill, hover colors)
* Interactive features (popups, navigation, legend)
* Center point coordinates

= Does the plugin work on mobile devices? =

Yes, the maps are fully responsive and work perfectly on mobile devices, tablets, and desktops.

= Can users navigate between different administrative levels? =

Yes, if you enable click navigation, users can click on provinces to drill down to districts, and click on districts to drill down to LLGs.

= How is the boundary data stored? =

The plugin includes GeoJSON files with official Papua New Guinea administrative boundary data from 2011. This data is cached for performance and loaded dynamically as needed.

== Screenshots ==

1. Admin interface showing the map creation wizard
2. Map list view with all created maps
3. Provincial level map with interactive boundaries
4. District level map with drill-down navigation
5. Settings page with cache management options

== Changelog ==

= 1.0.0 =
* Initial release
* Provincial, District, and LLG boundary support
* Interactive map creation interface
* Shortcode system for flexible map embedding
* Hierarchical navigation between administrative levels
* Customizable styling options
* Caching system for performance
* Mobile-responsive design

== Upgrade Notice ==

= 1.0.0 =
Initial release of the Dakoii Prov - Maps Plugin.

== Technical Requirements ==

* WordPress 5.0 or higher
* PHP 7.4 or higher
* Modern web browser with JavaScript enabled
* Sufficient server resources for JSON file processing

== Support ==

For support, documentation, and feature requests, please visit our support page or contact the plugin developers.

== Credits ==

* Plugin Development: Noland Gande (<EMAIL>)
* Website: https://www.dakoiims.com
* Boundary data: Papua New Guinea administrative boundaries (2011)
* Mapping library: Leaflet.js
* Icons: WordPress Dashicons
* Development: Following WordPress coding standards and best practices
