<?php
/**
 * Cache Manager Class
 * Handles caching operations for the plugin
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DakoiiCacheManager {
    
    /**
     * Cache groups
     */
    private $cache_groups = array(
        'boundaries' => 'dakoii_boundaries',
        'maps' => 'dakoii_maps',
        'settings' => 'dakoii_settings'
    );
    
    /**
     * Default cache expiry times (in seconds)
     */
    private $cache_expiry = array(
        'boundaries' => 3600,    // 1 hour
        'maps' => 1800,          // 30 minutes
        'settings' => 7200       // 2 hours
    );
    
    /**
     * Constructor
     */
    public function __construct() {
        // Add hooks for cache management
        add_action('dakoii_map_created', array($this, 'clear_map_cache'));
        add_action('dakoii_map_updated', array($this, 'clear_map_cache'));
        add_action('dakoii_map_deleted', array($this, 'clear_map_cache'));
        
        // Add scheduled cache cleanup
        add_action('wp', array($this, 'schedule_cache_cleanup'));
        add_action('dakoii_maps_cache_cleanup', array($this, 'cleanup_expired_cache'));
        
        // Add admin hooks for cache management
        if (is_admin()) {
            add_action('wp_ajax_dakoii_clear_cache', array($this, 'ajax_clear_cache'));
        }
    }
    
    /**
     * Get cached data
     * 
     * @param string $key Cache key
     * @param string $group Cache group
     * @return mixed Cached data or false
     */
    public function get($key, $group = 'boundaries') {
        $cache_group = $this->get_cache_group($group);
        return wp_cache_get($key, $cache_group);
    }
    
    /**
     * Set cached data
     * 
     * @param string $key Cache key
     * @param mixed $data Data to cache
     * @param string $group Cache group
     * @param int $expiry Cache expiry time (optional)
     * @return bool Success status
     */
    public function set($key, $data, $group = 'boundaries', $expiry = null) {
        $cache_group = $this->get_cache_group($group);
        $expiry = $expiry ?: $this->get_cache_expiry($group);
        
        return wp_cache_set($key, $data, $cache_group, $expiry);
    }
    
    /**
     * Delete cached data
     * 
     * @param string $key Cache key
     * @param string $group Cache group
     * @return bool Success status
     */
    public function delete($key, $group = 'boundaries') {
        $cache_group = $this->get_cache_group($group);
        return wp_cache_delete($key, $cache_group);
    }
    
    /**
     * Clear all cache for a group
     * 
     * @param string $group Cache group
     * @return bool Success status
     */
    public function clear_group($group) {
        $cache_group = $this->get_cache_group($group);
        
        // WordPress doesn't have a built-in group flush, so we'll use a workaround
        if (function_exists('wp_cache_flush_group')) {
            return wp_cache_flush_group($cache_group);
        }
        
        // Fallback: increment group version to invalidate all keys
        return $this->increment_group_version($group);
    }
    
    /**
     * Clear all plugin cache
     * 
     * @return bool Success status
     */
    public function clear_all() {
        $success = true;
        
        foreach (array_keys($this->cache_groups) as $group) {
            if (!$this->clear_group($group)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Get cache statistics
     * 
     * @return array Cache statistics
     */
    public function get_cache_stats() {
        $stats = array(
            'enabled' => $this->is_cache_enabled(),
            'groups' => array()
        );
        
        foreach ($this->cache_groups as $group => $cache_group) {
            $stats['groups'][$group] = array(
                'name' => $cache_group,
                'expiry' => $this->get_cache_expiry($group),
                'version' => $this->get_group_version($group)
            );
        }
        
        return $stats;
    }
    
    /**
     * Check if caching is enabled
     * 
     * @return bool Cache enabled status
     */
    public function is_cache_enabled() {
        $settings = get_option('dakoii_maps_settings', array());
        return isset($settings['cache_enabled']) ? (bool)$settings['cache_enabled'] : true;
    }
    
    /**
     * Enable or disable caching
     * 
     * @param bool $enabled Cache enabled status
     * @return bool Success status
     */
    public function set_cache_enabled($enabled) {
        $settings = get_option('dakoii_maps_settings', array());
        $settings['cache_enabled'] = (bool)$enabled;
        
        return update_option('dakoii_maps_settings', $settings);
    }
    
    /**
     * Get cache group name
     * 
     * @param string $group Group identifier
     * @return string Cache group name
     */
    private function get_cache_group($group) {
        if (!isset($this->cache_groups[$group])) {
            return $this->cache_groups['boundaries']; // Default fallback
        }
        
        return $this->cache_groups[$group];
    }
    
    /**
     * Get cache expiry time for group
     * 
     * @param string $group Group identifier
     * @return int Expiry time in seconds
     */
    private function get_cache_expiry($group) {
        $settings = get_option('dakoii_maps_settings', array());
        
        // Check for custom expiry in settings
        $setting_key = $group . '_cache_expiry';
        if (isset($settings[$setting_key])) {
            return intval($settings[$setting_key]);
        }
        
        // Return default expiry
        return isset($this->cache_expiry[$group]) ? $this->cache_expiry[$group] : 3600;
    }
    
    /**
     * Get group version for cache invalidation
     * 
     * @param string $group Group identifier
     * @return int Group version
     */
    private function get_group_version($group) {
        $option_key = 'dakoii_cache_version_' . $group;
        $version = get_option($option_key, 1);
        
        return intval($version);
    }
    
    /**
     * Increment group version to invalidate cache
     * 
     * @param string $group Group identifier
     * @return bool Success status
     */
    private function increment_group_version($group) {
        $option_key = 'dakoii_cache_version_' . $group;
        $current_version = $this->get_group_version($group);
        
        return update_option($option_key, $current_version + 1);
    }
    
    /**
     * Generate cache key with version
     * 
     * @param string $base_key Base cache key
     * @param string $group Group identifier
     * @return string Versioned cache key
     */
    public function get_versioned_key($base_key, $group = 'boundaries') {
        $version = $this->get_group_version($group);
        return $base_key . '_v' . $version;
    }
    
    /**
     * Clear map-related cache
     * 
     * @param int $map_id Map ID (optional)
     */
    public function clear_map_cache($map_id = null) {
        // Clear maps cache
        $this->clear_group('maps');
        
        // Clear boundaries cache as it might be affected
        $this->clear_group('boundaries');
        
        // Fire action for other components
        do_action('dakoii_cache_cleared', 'maps', $map_id);
    }
    
    /**
     * Clear boundary cache
     * 
     * @param string $type Boundary type (optional)
     */
    public function clear_boundary_cache($type = null) {
        if ($type) {
            // Clear specific boundary type cache
            $this->delete('boundaries_' . $type, 'boundaries');
        } else {
            // Clear all boundary cache
            $this->clear_group('boundaries');
        }
        
        // Fire action for other components
        do_action('dakoii_cache_cleared', 'boundaries', $type);
    }
    
    /**
     * Schedule cache cleanup
     */
    public function schedule_cache_cleanup() {
        if (!wp_next_scheduled('dakoii_maps_cache_cleanup')) {
            wp_schedule_event(time(), 'daily', 'dakoii_maps_cache_cleanup');
        }
    }
    
    /**
     * Cleanup expired cache entries
     */
    public function cleanup_expired_cache() {
        // This is a placeholder for cache cleanup logic
        // In practice, WordPress handles cache expiry automatically
        // But we can use this to clean up any custom cache storage
        
        do_action('dakoii_cache_cleanup');
    }
    
    /**
     * Get cache size (if supported by cache backend)
     * 
     * @return array Cache size information
     */
    public function get_cache_size() {
        $size_info = array(
            'supported' => false,
            'total_size' => 0,
            'groups' => array()
        );
        
        // This would need to be implemented based on the cache backend
        // Most WordPress cache implementations don't provide size information
        
        return $size_info;
    }
    
    /**
     * Warm up cache with commonly used data
     */
    public function warm_cache() {
        // Load and cache provincial boundaries
        $boundary_loader = new DakoniiBoundaryLoader();
        $boundary_loader->load_provincial_boundaries();
        
        // Load and cache active maps
        $map_manager = new DakoiiMapManager();
        $maps = $map_manager->get_all_maps(array('status' => 'active', 'limit' => 10));
        
        // Cache each map's boundary data
        foreach ($maps as $map) {
            $boundary_loader->load_boundaries($map->map_level);
        }
        
        do_action('dakoii_cache_warmed');
    }
    
    /**
     * AJAX: Clear cache
     */
    public function ajax_clear_cache() {
        check_ajax_referer('dakoii_maps_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die(__('Permission denied', 'dakoii-maps'));
        }
        
        $cache_type = sanitize_text_field($_POST['cache_type'] ?? 'all');
        
        switch ($cache_type) {
            case 'boundaries':
                $success = $this->clear_group('boundaries');
                $message = __('Boundary cache cleared', 'dakoii-maps');
                break;
                
            case 'maps':
                $success = $this->clear_group('maps');
                $message = __('Map cache cleared', 'dakoii-maps');
                break;
                
            case 'all':
            default:
                $success = $this->clear_all();
                $message = __('All cache cleared', 'dakoii-maps');
                break;
        }
        
        if ($success) {
            wp_send_json_success($message);
        } else {
            wp_send_json_error(__('Failed to clear cache', 'dakoii-maps'));
        }
    }
}
