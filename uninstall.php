<?php
/**
 * Uninstall script for Dakoii Prov - Maps Plugin
 * 
 * This file is executed when the plugin is deleted from WordPress admin.
 * It cleans up all plugin data including database tables and options.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

/**
 * Remove plugin data
 */
function dakoii_maps_uninstall() {
    global $wpdb;
    
    // Remove database tables
    $tables = array(
        $wpdb->prefix . 'dakoii_map_boundaries',  // Remove this first due to foreign key
        $wpdb->prefix . 'dakoii_maps'
    );
    
    foreach ($tables as $table) {
        $wpdb->query("DROP TABLE IF EXISTS $table");
    }
    
    // Remove plugin options
    $options = array(
        'dakoii_maps_settings',
        'dakoii_maps_version',
        'dakoii_maps_db_version',
        'dakoii_maps_activated'
    );
    
    foreach ($options as $option) {
        delete_option($option);
    }
    
    // Remove cache version options
    $cache_groups = array('boundaries', 'maps', 'settings');
    foreach ($cache_groups as $group) {
        delete_option('dakoii_cache_version_' . $group);
    }
    
    // Clear any scheduled events
    wp_clear_scheduled_hook('dakoii_maps_cache_cleanup');
    
    // Clear object cache
    if (function_exists('wp_cache_flush_group')) {
        wp_cache_flush_group('dakoii_boundaries');
        wp_cache_flush_group('dakoii_maps');
        wp_cache_flush_group('dakoii_settings');
    }
    
    // Remove user meta related to plugin
    $wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'dakoii_maps_%'");
    
    // Log uninstall
    error_log('Dakoii Maps Plugin: Uninstall completed successfully');
}

// Execute uninstall
dakoii_maps_uninstall();
