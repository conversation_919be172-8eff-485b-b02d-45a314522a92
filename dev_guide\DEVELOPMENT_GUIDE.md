# Dakoii Prov - Maps Plugin Development Guide

## Overview

The **Dakoii Prov - Maps Plugin** is a WordPress plugin designed to display interactive maps of Papua New Guinea at three administrative levels:
- **Provincial Level** (22 provinces)
- **District Level** (87 districts)
- **Local Level Government (LLG)** (326 LLGs)

The plugin allows users to create multiple maps with different configurations and generates unique shortcodes for each map, enabling flexible placement throughout WordPress sites.

## System Architecture

### 1. Data Structure

The plugin utilizes three GeoJSON files containing boundary data:

#### Provincial Boundaries (`png_prov_boundaries_2011.json`)
- **Features**: 22 provinces
- **Properties**: 
  - `FID`: Feature ID
  - `PROVID`: Province ID (e.g., "OCNPNG001004")
  - `PROVNAME`: Province Name (e.g., "National Capital District")

#### District Boundaries (`png_dist_boundaries_2011.json`)
- **Features**: 87 districts
- **Properties**:
  - `FID`: Feature ID
  - `GEOCODE`: Geographic Code (e.g., "OCNPNG00202105")
  - `DISTNAME`: District Name (e.g., "Koroba/Kopiago District")

#### LLG Boundaries (`png_llg_boundaries_2011.json`)
- **Features**: 326 LLGs
- **Properties**:
  - `FID`: Feature ID
  - `GEOCODE`: Geographic Code (e.g., "OCNPNG0010010209")
  - `LLGNAME`: LLG Name (e.g., "Olsobip Rural LLG")

### 2. Geographic Code Hierarchy

The GEOCODE follows a hierarchical structure:
```
OCNPNG + [Region] + [Province] + [District] + [LLG]
```

Example breakdown:
- `OCNPNG001004` (Province: National Capital District)
- `OCNPNG00100401` (District: National Capital District)
- `OCNPNG0010040101` (LLG: National Capital District)

## Plugin Structure

```
dakoii-prov-map-boundries/
├── assets/
│   ├── png_prov_boundaries_2011.json
│   ├── png_dist_boundaries_2011.json
│   ├── png_llg_boundaries_2011.json
│   ├── css/
│   │   └── admin-style.css
│   │   └── frontend-style.css
│   └── js/
│       ├── admin-script.js
│       ├── frontend-script.js
│       └── leaflet/
├── includes/
│   ├── class-map-manager.php
│   ├── class-boundary-loader.php
│   ├── class-shortcode-handler.php
│   └── class-admin-interface.php
├── templates/
│   ├── admin/
│   │   ├── map-list.php
│   │   ├── map-create.php
│   │   └── map-edit.php
│   └── frontend/
│       └── map-display.php
├── dakoii-prov-maps.php (Main plugin file)
└── uninstall.php
```

## Core Features

### 1. Map Creation Interface
- **Admin Panel**: WordPress admin interface for creating and managing maps
- **Map Configuration**:
  - Map name and description
  - Administrative level selection (Provincial/District/LLG)
  - Geographic scope selection
  - Display options (colors, zoom levels, etc.)
  - Shortcode generation

### 2. Hierarchical Map Display
- **Provincial Maps**: Show all provinces with ability to drill down
- **District Maps**: Show districts within selected province(s)
- **LLG Maps**: Show LLGs within selected district(s)
- **Interactive Navigation**: Click-through functionality between levels

### 3. Shortcode System
- **Unique Shortcodes**: Each map gets a unique shortcode
- **Flexible Placement**: Maps can be embedded in any post/page
- **Parameter Support**: Shortcode attributes for customization

## Technical Implementation

### 1. Database Schema

#### Maps Table (`wp_dakoii_maps`)
```sql
CREATE TABLE wp_dakoii_maps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    map_level ENUM('provincial', 'district', 'llg') NOT NULL,
    config JSON NOT NULL,
    shortcode VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### Map Boundaries Table (`wp_dakoii_map_boundaries`)
```sql
CREATE TABLE wp_dakoii_map_boundaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    map_id INT NOT NULL,
    boundary_type ENUM('province', 'district', 'llg') NOT NULL,
    boundary_id VARCHAR(50) NOT NULL,
    boundary_name VARCHAR(255) NOT NULL,
    geocode VARCHAR(50),
    FOREIGN KEY (map_id) REFERENCES wp_dakoii_maps(id) ON DELETE CASCADE
);
```

### 2. Frontend Technologies

#### JavaScript Libraries
- **Leaflet.js**: Primary mapping library
- **jQuery**: DOM manipulation and AJAX
- **Custom Scripts**: Plugin-specific functionality

#### CSS Framework
- **Custom CSS**: Responsive design for map containers
- **Admin Styles**: WordPress admin interface styling

### 3. PHP Classes

#### MapManager Class
```php
class DakoiiMapManager {
    public function createMap($config);
    public function updateMap($id, $config);
    public function deleteMap($id);
    public function getMap($id);
    public function getAllMaps();
    public function generateShortcode();
}
```

#### BoundaryLoader Class
```php
class DakoniiBoundaryLoader {
    public function loadProvincialBoundaries();
    public function loadDistrictBoundaries($provinceFilter = null);
    public function loadLLGBoundaries($districtFilter = null);
    public function getBoundaryHierarchy($geocode);
}
```

## User Interface Design

### 1. Admin Interface

#### Map List Page
- Table view of all created maps
- Quick actions: Edit, Delete, Copy Shortcode
- Bulk actions support
- Search and filter functionality

#### Map Creation/Edit Page
- **Step 1**: Basic Information (Name, Description)
- **Step 2**: Map Level Selection
- **Step 3**: Geographic Scope Selection
- **Step 4**: Display Configuration
- **Step 5**: Preview and Shortcode Generation

### 2. Frontend Display

#### Map Container
- Responsive design
- Loading indicators
- Error handling
- Interactive controls

#### Navigation Controls
- Zoom in/out buttons
- Reset view button
- Layer toggle controls
- Information panel

## Development Phases

### Phase 1: Core Infrastructure (Week 1-2)
1. Plugin structure setup
2. Database schema creation
3. Basic admin interface
4. Boundary data loading system

### Phase 2: Map Creation System (Week 3-4)
1. Admin interface for map creation
2. Geographic scope selection
3. Configuration options
4. Shortcode generation

### Phase 3: Frontend Display (Week 5-6)
1. Leaflet.js integration
2. Map rendering system
3. Interactive features
4. Responsive design

### Phase 4: Advanced Features (Week 7-8)
1. Hierarchical navigation
2. Search functionality
3. Custom styling options
4. Performance optimization

### Phase 5: Testing & Polish (Week 9-10)
1. Cross-browser testing
2. Performance testing
3. User acceptance testing
4. Documentation completion

## API Endpoints

### REST API Routes
```php
// Get map data
GET /wp-json/dakoii-maps/v1/maps/{id}

// Get boundary data
GET /wp-json/dakoii-maps/v1/boundaries/{type}

// Get filtered boundaries
GET /wp-json/dakoii-maps/v1/boundaries/{type}?filter={geocode}
```

### AJAX Endpoints
```php
// Admin actions
wp_ajax_dakoii_create_map
wp_ajax_dakoii_update_map
wp_ajax_dakoii_delete_map
wp_ajax_dakoii_get_boundaries

// Frontend actions
wp_ajax_nopriv_dakoii_get_map_data
wp_ajax_nopriv_dakoii_get_boundary_details
```

## Configuration Options

### Map Display Settings
- **Colors**: Boundary colors, fill colors, hover colors
- **Zoom**: Initial zoom level, min/max zoom
- **Size**: Map container dimensions
- **Controls**: Which controls to display
- **Popup**: Information popup configuration

### Geographic Filters
- **Provincial Level**: Select specific provinces
- **District Level**: Select specific districts within provinces
- **LLG Level**: Select specific LLGs within districts

## Security Considerations

### Data Validation
- Input sanitization for all user inputs
- Nonce verification for admin actions
- Capability checks for admin access

### File Security
- JSON file access restrictions
- Secure file upload handling
- XSS prevention in output

## Performance Optimization

### Caching Strategy
- Boundary data caching
- Map configuration caching
- Browser-side caching for static assets

### Data Loading
- Lazy loading for large datasets
- Progressive enhancement
- Optimized JSON parsing

## Browser Compatibility

### Supported Browsers
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+
- Internet Explorer 11 (limited support)

### Responsive Design
- Mobile-first approach
- Touch-friendly controls
- Adaptive layout for different screen sizes

## Deployment Checklist

### Pre-deployment
- [ ] Code review completed
- [ ] Security audit passed
- [ ] Performance testing completed
- [ ] Cross-browser testing completed
- [ ] Documentation updated

### Deployment
- [ ] Plugin files uploaded
- [ ] Database migrations run
- [ ] Asset files properly loaded
- [ ] Shortcodes tested
- [ ] Admin interface verified

### Post-deployment
- [ ] Functionality testing
- [ ] Performance monitoring
- [ ] User feedback collection
- [ ] Bug tracking setup
- [ ] Update documentation

## Maintenance & Updates

### Regular Maintenance
- Boundary data updates
- Security patches
- Performance monitoring
- User support

### Version Control
- Semantic versioning
- Change log maintenance
- Backward compatibility
- Migration scripts

This development guide provides a comprehensive roadmap for building the Dakoii Prov - Maps Plugin. Each section can be expanded with more detailed technical specifications as development progresses.
