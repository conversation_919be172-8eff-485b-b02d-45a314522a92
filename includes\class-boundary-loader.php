<?php
/**
 * Boundary Loader Class
 * Handles loading and filtering of boundary data from JSON files
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class DakoniiBoundaryLoader {
    
    /**
     * Cache group for boundary data
     */
    private $cache_group = 'dakoii_boundaries';
    
    /**
     * Cache expiry time in seconds
     */
    private $cache_expiry = 3600; // 1 hour
    
    /**
     * File mapping for boundary types
     */
    private $file_map = array(
        'provincial' => 'png_prov_boundaries_2011.json',
        'district' => 'png_dist_boundaries_2011.json',
        'llg' => 'png_llg_boundaries_2011.json'
    );
    
    /**
     * Constructor
     */
    public function __construct() {
        // Add AJAX hooks
        add_action('wp_ajax_dakoii_get_boundaries', array($this, 'ajax_get_boundaries'));
        add_action('wp_ajax_nopriv_dakoii_get_boundaries', array($this, 'ajax_get_boundaries'));
        add_action('wp_ajax_dakoii_get_child_boundaries', array($this, 'ajax_get_child_boundaries'));
        add_action('wp_ajax_nopriv_dakoii_get_child_boundaries', array($this, 'ajax_get_child_boundaries'));
    }
    
    /**
     * Load boundaries by type with optional filtering
     * 
     * @param string $type Boundary type (provincial, district, llg)
     * @param array $filters Optional filters
     * @return array Boundary data
     */
    public function load_boundaries($type, $filters = array()) {
        $cache_key = $this->generate_cache_key($type, $filters);
        $boundaries = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $boundaries) {
            $boundaries = $this->load_boundaries_from_file($type);
            
            if (!empty($filters)) {
                $boundaries = $this->apply_filters($boundaries, $filters);
            }
            
            wp_cache_set($cache_key, $boundaries, $this->cache_group, $this->cache_expiry);
        }
        
        return $boundaries;
    }
    
    /**
     * Load provincial boundaries
     * 
     * @param array $filters Optional filters
     * @return array Provincial boundary data
     */
    public function load_provincial_boundaries($filters = array()) {
        return $this->load_boundaries('provincial', $filters);
    }
    
    /**
     * Load district boundaries
     * 
     * @param array $province_filter Province codes to filter by
     * @return array District boundary data
     */
    public function load_district_boundaries($province_filter = array()) {
        $filters = array();
        
        if (!empty($province_filter)) {
            $filters['geocode_prefix'] = $province_filter;
        }
        
        return $this->load_boundaries('district', $filters);
    }
    
    /**
     * Load LLG boundaries
     * 
     * @param array $district_filter District codes to filter by
     * @return array LLG boundary data
     */
    public function load_llg_boundaries($district_filter = array()) {
        $filters = array();
        
        if (!empty($district_filter)) {
            $filters['geocode_prefix'] = $district_filter;
        }
        
        return $this->load_boundaries('llg', $filters);
    }
    
    /**
     * Get child boundaries for a parent geocode
     * 
     * @param string $parent_geocode Parent geocode
     * @param string $child_level Child level (district, llg)
     * @return array Child boundary data
     */
    public function get_child_boundaries($parent_geocode, $child_level) {
        $filters = array(
            'geocode_prefix' => array($parent_geocode)
        );
        
        return $this->load_boundaries($child_level, $filters);
    }
    
    /**
     * Get boundary hierarchy information
     * 
     * @param string $geocode Geocode to analyze
     * @return array Hierarchy information
     */
    public function get_boundary_hierarchy($geocode) {
        $hierarchy = array();
        
        // Remove OCNPNG prefix for analysis
        $code = str_replace('OCNPNG', '', $geocode);
        
        // Province level (first 6 digits)
        if (strlen($code) >= 6) {
            $hierarchy['province'] = 'OCNPNG' . substr($code, 0, 6);
        }
        
        // District level (first 8 digits)
        if (strlen($code) >= 8) {
            $hierarchy['district'] = 'OCNPNG' . substr($code, 0, 8);
        }
        
        // LLG level (first 10 digits)
        if (strlen($code) >= 10) {
            $hierarchy['llg'] = 'OCNPNG' . substr($code, 0, 10);
        }
        
        return $hierarchy;
    }
    
    /**
     * Search boundaries by name
     * 
     * @param string $search_term Search term
     * @param string $type Boundary type
     * @return array Matching boundaries
     */
    public function search_boundaries($search_term, $type = 'all') {
        $results = array();
        $search_term = strtolower(trim($search_term));
        
        if (empty($search_term)) {
            return $results;
        }
        
        $types_to_search = ($type === 'all') ? array('provincial', 'district', 'llg') : array($type);
        
        foreach ($types_to_search as $boundary_type) {
            $boundaries = $this->load_boundaries($boundary_type);
            
            if (isset($boundaries['features'])) {
                foreach ($boundaries['features'] as $feature) {
                    $name = $this->get_boundary_name($feature, $boundary_type);
                    
                    if (stripos($name, $search_term) !== false) {
                        $results[] = array(
                            'type' => $boundary_type,
                            'name' => $name,
                            'geocode' => $this->get_boundary_geocode($feature, $boundary_type),
                            'feature' => $feature
                        );
                    }
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Load boundaries from JSON file
     * 
     * @param string $type Boundary type
     * @return array Boundary data
     */
    private function load_boundaries_from_file($type) {
        if (!isset($this->file_map[$type])) {
            error_log("Dakoii Maps: Invalid boundary type: {$type}");
            return array();
        }
        
        $file_path = DAKOII_MAPS_PLUGIN_DIR . 'assets/' . $this->file_map[$type];
        
        if (!file_exists($file_path)) {
            error_log("Dakoii Maps: Boundary file not found: {$file_path}");
            return array();
        }
        
        $json_content = file_get_contents($file_path);
        
        if ($json_content === false) {
            error_log("Dakoii Maps: Failed to read boundary file: {$file_path}");
            return array();
        }
        
        $data = json_decode($json_content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Dakoii Maps: JSON decode error in {$file_path}: " . json_last_error_msg());
            return array();
        }
        
        return $data;
    }
    
    /**
     * Apply filters to boundary data
     * 
     * @param array $boundaries Boundary data
     * @param array $filters Filters to apply
     * @return array Filtered boundary data
     */
    private function apply_filters($boundaries, $filters) {
        if (!isset($boundaries['features']) || empty($filters)) {
            return $boundaries;
        }
        
        $filtered_features = array();
        
        foreach ($boundaries['features'] as $feature) {
            if ($this->feature_matches_filters($feature, $filters)) {
                $filtered_features[] = $feature;
            }
        }
        
        $boundaries['features'] = $filtered_features;
        return $boundaries;
    }
    
    /**
     * Check if feature matches filters
     * 
     * @param array $feature GeoJSON feature
     * @param array $filters Filters to check
     * @return bool True if matches
     */
    private function feature_matches_filters($feature, $filters) {
        if (!isset($feature['properties'])) {
            return false;
        }
        
        $properties = $feature['properties'];
        
        // Filter by geocode prefix
        if (isset($filters['geocode_prefix'])) {
            $geocode = $this->get_feature_geocode($properties);
            
            foreach ($filters['geocode_prefix'] as $prefix) {
                if (strpos($geocode, $prefix) === 0) {
                    return true;
                }
            }
            return false;
        }
        
        // Filter by specific IDs
        if (isset($filters['ids'])) {
            $id = isset($properties['FID']) ? $properties['FID'] : null;
            return in_array($id, $filters['ids']);
        }
        
        // Filter by names
        if (isset($filters['names'])) {
            $name = $this->get_feature_name($properties);
            return in_array($name, $filters['names']);
        }
        
        return true;
    }
    
    /**
     * Get geocode from feature properties
     * 
     * @param array $properties Feature properties
     * @return string Geocode
     */
    private function get_feature_geocode($properties) {
        return isset($properties['GEOCODE']) ? $properties['GEOCODE'] : 
               (isset($properties['PROVID']) ? $properties['PROVID'] : '');
    }
    
    /**
     * Get name from feature properties
     * 
     * @param array $properties Feature properties
     * @return string Name
     */
    private function get_feature_name($properties) {
        return isset($properties['PROVNAME']) ? $properties['PROVNAME'] :
               (isset($properties['DISTNAME']) ? $properties['DISTNAME'] :
               (isset($properties['LLGNAME']) ? $properties['LLGNAME'] : ''));
    }
    
    /**
     * Get boundary name from feature
     * 
     * @param array $feature GeoJSON feature
     * @param string $type Boundary type
     * @return string Boundary name
     */
    private function get_boundary_name($feature, $type) {
        if (!isset($feature['properties'])) {
            return '';
        }
        
        $properties = $feature['properties'];
        
        switch ($type) {
            case 'provincial':
                return isset($properties['PROVNAME']) ? $properties['PROVNAME'] : '';
            case 'district':
                return isset($properties['DISTNAME']) ? $properties['DISTNAME'] : '';
            case 'llg':
                return isset($properties['LLGNAME']) ? $properties['LLGNAME'] : '';
            default:
                return $this->get_feature_name($properties);
        }
    }
    
    /**
     * Get boundary geocode from feature
     * 
     * @param array $feature GeoJSON feature
     * @param string $type Boundary type
     * @return string Boundary geocode
     */
    private function get_boundary_geocode($feature, $type) {
        if (!isset($feature['properties'])) {
            return '';
        }
        
        return $this->get_feature_geocode($feature['properties']);
    }
    
    /**
     * Generate cache key
     * 
     * @param string $type Boundary type
     * @param array $filters Filters
     * @return string Cache key
     */
    private function generate_cache_key($type, $filters) {
        return 'boundaries_' . $type . '_' . md5(serialize($filters));
    }
    
    /**
     * Clear boundary cache
     * 
     * @param string $type Optional boundary type to clear specific cache
     */
    public function clear_cache($type = null) {
        if ($type) {
            wp_cache_delete('boundaries_' . $type, $this->cache_group);
        } else {
            wp_cache_flush_group($this->cache_group);
        }
    }
    
    /**
     * AJAX: Get boundaries
     */
    public function ajax_get_boundaries() {
        check_ajax_referer('dakoii_maps_nonce', 'nonce');
        
        $type = sanitize_text_field($_POST['type'] ?? '');
        $filters = $_POST['filters'] ?? array();
        
        if (empty($type)) {
            wp_send_json_error(__('Boundary type is required', 'dakoii-maps'));
        }
        
        $boundaries = $this->load_boundaries($type, $filters);
        
        if (empty($boundaries)) {
            wp_send_json_error(__('No boundaries found', 'dakoii-maps'));
        }
        
        wp_send_json_success($boundaries);
    }
    
    /**
     * AJAX: Get child boundaries
     */
    public function ajax_get_child_boundaries() {
        check_ajax_referer('dakoii_maps_nonce', 'nonce');
        
        $parent_geocode = sanitize_text_field($_POST['parent_geocode'] ?? '');
        $child_level = sanitize_text_field($_POST['child_level'] ?? '');
        
        if (empty($parent_geocode) || empty($child_level)) {
            wp_send_json_error(__('Parent geocode and child level are required', 'dakoii-maps'));
        }
        
        $boundaries = $this->get_child_boundaries($parent_geocode, $child_level);
        
        if (empty($boundaries)) {
            wp_send_json_error(__('No child boundaries found', 'dakoii-maps'));
        }
        
        wp_send_json_success($boundaries);
    }
}
