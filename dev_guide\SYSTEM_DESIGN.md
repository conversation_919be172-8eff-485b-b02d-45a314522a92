# Dakoii Prov - Maps Plugin System Design

## System Overview

The Dakoii Prov - Maps Plugin is designed as a modular WordPress plugin that provides interactive mapping capabilities for Papua New Guinea's administrative boundaries. The system follows WordPress best practices and implements a clean separation of concerns.

## Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│                    WordPress Frontend                       │
├─────────────────────────────────────────────────────────────┤
│  Shortcode Handler  │  Map Display  │  User Interactions   │
├─────────────────────────────────────────────────────────────┤
│                    WordPress Admin                          │
├─────────────────────────────────────────────────────────────┤
│  Admin Interface   │  Map Manager   │  Settings Panel      │
├─────────────────────────────────────────────────────────────┤
│                    Plugin Core Layer                        │
├─────────────────────────────────────────────────────────────┤
│  Map Manager  │  Boundary Loader  │  Shortcode Generator   │
├─────────────────────────────────────────────────────────────┤
│                    Data Access Layer                        │
├─────────────────────────────────────────────────────────────┤
│  WordPress DB  │  JSON Files  │  Cache System  │  REST API │
└─────────────────────────────────────────────────────────────┘
```

## Core Components

### 1. Plugin Entry Point (`dakoii-prov-maps.php`)

```php
<?php
/**
 * Plugin Name: Dakoii Prov - Maps Plugin
 * Description: Interactive maps for Papua New Guinea administrative boundaries
 * Version: 1.0.0
 * Author: Your Name
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('DAKOII_MAPS_VERSION', '1.0.0');
define('DAKOII_MAPS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DAKOII_MAPS_PLUGIN_URL', plugin_dir_url(__FILE__));

// Main plugin class
class DakoiiProvMaps {
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        $this->load_dependencies();
        $this->init_hooks();
    }
    
    private function load_dependencies() {
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-map-manager.php';
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-boundary-loader.php';
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-shortcode-handler.php';
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-admin-interface.php';
    }
    
    private function init_hooks() {
        // Initialize components
        new DakoiiMapManager();
        new DakoniiBoundaryLoader();
        new DakoiiShortcodeHandler();
        
        if (is_admin()) {
            new DakoiiAdminInterface();
        }
    }
    
    public function activate() {
        $this->create_tables();
        $this->set_default_options();
    }
    
    public function deactivate() {
        // Cleanup if needed
    }
}

// Initialize the plugin
new DakoiiProvMaps();
```

### 2. Map Manager Class (`includes/class-map-manager.php`)

```php
<?php
class DakoiiMapManager {
    private $table_name;
    private $boundaries_table;
    
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'dakoii_maps';
        $this->boundaries_table = $wpdb->prefix . 'dakoii_map_boundaries';
        
        add_action('wp_ajax_dakoii_create_map', array($this, 'ajax_create_map'));
        add_action('wp_ajax_dakoii_update_map', array($this, 'ajax_update_map'));
        add_action('wp_ajax_dakoii_delete_map', array($this, 'ajax_delete_map'));
    }
    
    public function create_map($config) {
        global $wpdb;
        
        $shortcode = $this->generate_unique_shortcode();
        
        $result = $wpdb->insert(
            $this->table_name,
            array(
                'name' => sanitize_text_field($config['name']),
                'description' => sanitize_textarea_field($config['description']),
                'map_level' => sanitize_text_field($config['map_level']),
                'config' => wp_json_encode($config),
                'shortcode' => $shortcode
            ),
            array('%s', '%s', '%s', '%s', '%s')
        );
        
        if ($result) {
            $map_id = $wpdb->insert_id;
            $this->save_map_boundaries($map_id, $config['boundaries']);
            return $map_id;
        }
        
        return false;
    }
    
    public function get_map($id) {
        global $wpdb;
        
        $map = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE id = %d", $id)
        );
        
        if ($map) {
            $map->config = json_decode($map->config, true);
            $map->boundaries = $this->get_map_boundaries($id);
        }
        
        return $map;
    }
    
    public function get_map_by_shortcode($shortcode) {
        global $wpdb;
        
        $map = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$this->table_name} WHERE shortcode = %s", $shortcode)
        );
        
        if ($map) {
            $map->config = json_decode($map->config, true);
            $map->boundaries = $this->get_map_boundaries($map->id);
        }
        
        return $map;
    }
    
    private function generate_unique_shortcode() {
        global $wpdb;
        
        do {
            $shortcode = 'dakoii_map_' . wp_generate_password(8, false);
            $exists = $wpdb->get_var(
                $wpdb->prepare("SELECT COUNT(*) FROM {$this->table_name} WHERE shortcode = %s", $shortcode)
            );
        } while ($exists > 0);
        
        return $shortcode;
    }
    
    private function save_map_boundaries($map_id, $boundaries) {
        global $wpdb;
        
        // Clear existing boundaries
        $wpdb->delete($this->boundaries_table, array('map_id' => $map_id));
        
        // Insert new boundaries
        foreach ($boundaries as $boundary) {
            $wpdb->insert(
                $this->boundaries_table,
                array(
                    'map_id' => $map_id,
                    'boundary_type' => $boundary['type'],
                    'boundary_id' => $boundary['id'],
                    'boundary_name' => $boundary['name'],
                    'geocode' => $boundary['geocode']
                ),
                array('%d', '%s', '%s', '%s', '%s')
            );
        }
    }
    
    private function get_map_boundaries($map_id) {
        global $wpdb;
        
        return $wpdb->get_results(
            $wpdb->prepare("SELECT * FROM {$this->boundaries_table} WHERE map_id = %d", $map_id)
        );
    }
}
```

### 3. Boundary Loader Class (`includes/class-boundary-loader.php`)

```php
<?php
class DakoniiBoundaryLoader {
    private $cache_group = 'dakoii_boundaries';
    private $cache_expiry = 3600; // 1 hour
    
    public function __construct() {
        add_action('wp_ajax_dakoii_get_boundaries', array($this, 'ajax_get_boundaries'));
        add_action('wp_ajax_nopriv_dakoii_get_boundaries', array($this, 'ajax_get_boundaries'));
    }
    
    public function load_provincial_boundaries() {
        $cache_key = 'provincial_boundaries';
        $boundaries = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $boundaries) {
            $file_path = DAKOII_MAPS_PLUGIN_DIR . 'assets/png_prov_boundaries_2011.json';
            $boundaries = $this->load_json_file($file_path);
            wp_cache_set($cache_key, $boundaries, $this->cache_group, $this->cache_expiry);
        }
        
        return $boundaries;
    }
    
    public function load_district_boundaries($province_filter = null) {
        $cache_key = 'district_boundaries_' . md5(serialize($province_filter));
        $boundaries = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $boundaries) {
            $file_path = DAKOII_MAPS_PLUGIN_DIR . 'assets/png_dist_boundaries_2011.json';
            $all_boundaries = $this->load_json_file($file_path);
            
            if ($province_filter) {
                $boundaries = $this->filter_boundaries_by_province($all_boundaries, $province_filter);
            } else {
                $boundaries = $all_boundaries;
            }
            
            wp_cache_set($cache_key, $boundaries, $this->cache_group, $this->cache_expiry);
        }
        
        return $boundaries;
    }
    
    public function load_llg_boundaries($district_filter = null) {
        $cache_key = 'llg_boundaries_' . md5(serialize($district_filter));
        $boundaries = wp_cache_get($cache_key, $this->cache_group);
        
        if (false === $boundaries) {
            $file_path = DAKOII_MAPS_PLUGIN_DIR . 'assets/png_llg_boundaries_2011.json';
            $all_boundaries = $this->load_json_file($file_path);
            
            if ($district_filter) {
                $boundaries = $this->filter_boundaries_by_district($all_boundaries, $district_filter);
            } else {
                $boundaries = $all_boundaries;
            }
            
            wp_cache_set($cache_key, $boundaries, $this->cache_group, $this->cache_expiry);
        }
        
        return $boundaries;
    }
    
    private function load_json_file($file_path) {
        if (!file_exists($file_path)) {
            return array();
        }
        
        $json_content = file_get_contents($file_path);
        $data = json_decode($json_content, true);
        
        return $data ? $data : array();
    }
    
    private function filter_boundaries_by_province($boundaries, $province_codes) {
        if (!isset($boundaries['features'])) {
            return $boundaries;
        }
        
        $filtered_features = array();
        
        foreach ($boundaries['features'] as $feature) {
            if (isset($feature['properties']['GEOCODE'])) {
                $geocode = $feature['properties']['GEOCODE'];
                
                foreach ($province_codes as $province_code) {
                    if (strpos($geocode, $province_code) === 0) {
                        $filtered_features[] = $feature;
                        break;
                    }
                }
            }
        }
        
        $boundaries['features'] = $filtered_features;
        return $boundaries;
    }
    
    private function filter_boundaries_by_district($boundaries, $district_codes) {
        if (!isset($boundaries['features'])) {
            return $boundaries;
        }
        
        $filtered_features = array();
        
        foreach ($boundaries['features'] as $feature) {
            if (isset($feature['properties']['GEOCODE'])) {
                $geocode = $feature['properties']['GEOCODE'];
                
                foreach ($district_codes as $district_code) {
                    if (strpos($geocode, $district_code) === 0) {
                        $filtered_features[] = $feature;
                        break;
                    }
                }
            }
        }
        
        $boundaries['features'] = $filtered_features;
        return $boundaries;
    }
    
    public function get_boundary_hierarchy($geocode) {
        // Extract hierarchy information from geocode
        $hierarchy = array();
        
        // Province level (first 9 characters after OCNPNG)
        if (strlen($geocode) >= 15) {
            $hierarchy['province'] = substr($geocode, 0, 15);
        }
        
        // District level (first 11 characters after OCNPNG)
        if (strlen($geocode) >= 17) {
            $hierarchy['district'] = substr($geocode, 0, 17);
        }
        
        // LLG level (full geocode)
        if (strlen($geocode) >= 19) {
            $hierarchy['llg'] = $geocode;
        }
        
        return $hierarchy;
    }
    
    public function ajax_get_boundaries() {
        check_ajax_referer('dakoii_maps_nonce', 'nonce');
        
        $type = sanitize_text_field($_POST['type']);
        $filter = isset($_POST['filter']) ? $_POST['filter'] : null;
        
        switch ($type) {
            case 'provincial':
                $boundaries = $this->load_provincial_boundaries();
                break;
            case 'district':
                $boundaries = $this->load_district_boundaries($filter);
                break;
            case 'llg':
                $boundaries = $this->load_llg_boundaries($filter);
                break;
            default:
                wp_die('Invalid boundary type');
        }
        
        wp_send_json_success($boundaries);
    }
}
```

## Data Flow Architecture

### 1. Map Creation Flow

```
User Input → Admin Interface → Validation → Map Manager → Database Storage
     ↓
Boundary Selection → Boundary Loader → JSON Files → Cache → Database
     ↓
Shortcode Generation → Unique ID → Database Storage → User Display
```

### 2. Map Display Flow

```
Shortcode → Shortcode Handler → Map Manager → Database Query
     ↓
Map Config → Boundary Loader → Cache Check → JSON Files (if needed)
     ↓
Frontend Rendering → Leaflet.js → Interactive Map → User Interaction
```

### 3. Hierarchical Navigation Flow

```
User Click → JavaScript Event → AJAX Request → Boundary Loader
     ↓
Geocode Analysis → Hierarchy Detection → Filtered Data → Map Update
     ↓
New Boundaries → Leaflet Update → Visual Transition → User Feedback
```

## Database Design

### Tables Structure

```sql
-- Maps table
CREATE TABLE wp_dakoii_maps (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    map_level ENUM('provincial', 'district', 'llg') NOT NULL,
    config JSON NOT NULL,
    shortcode VARCHAR(50) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_shortcode (shortcode),
    INDEX idx_map_level (map_level),
    INDEX idx_created_at (created_at)
);

-- Map boundaries table
CREATE TABLE wp_dakoii_map_boundaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    map_id INT NOT NULL,
    boundary_type ENUM('province', 'district', 'llg') NOT NULL,
    boundary_id VARCHAR(50) NOT NULL,
    boundary_name VARCHAR(255) NOT NULL,
    geocode VARCHAR(50),
    FOREIGN KEY (map_id) REFERENCES wp_dakoii_maps(id) ON DELETE CASCADE,
    INDEX idx_map_id (map_id),
    INDEX idx_boundary_type (boundary_type),
    INDEX idx_geocode (geocode)
);

-- Plugin options (using WordPress options table)
-- wp_options entries:
-- - dakoii_maps_version
-- - dakoii_maps_settings
-- - dakoii_maps_cache_settings
```

### Configuration JSON Structure

```json
{
    "map_level": "provincial",
    "display_options": {
        "width": "100%",
        "height": "500px",
        "zoom": {
            "initial": 6,
            "min": 4,
            "max": 12
        },
        "center": {
            "lat": -6.314993,
            "lng": 143.95555
        }
    },
    "styling": {
        "boundary_color": "#3388ff",
        "boundary_width": 2,
        "fill_color": "#3388ff",
        "fill_opacity": 0.2,
        "hover_color": "#ff7800",
        "hover_opacity": 0.7
    },
    "features": {
        "popup_enabled": true,
        "click_navigation": true,
        "search_enabled": false,
        "legend_enabled": true
    },
    "boundaries": [
        {
            "type": "province",
            "id": "OCNPNG001004",
            "name": "National Capital District",
            "included": true
        }
    ]
}
```

## Security Implementation

### 1. Input Validation
- Sanitize all user inputs using WordPress functions
- Validate JSON configuration data
- Check file permissions for JSON assets

### 2. Access Control
- Capability checks for admin functions
- Nonce verification for AJAX requests
- Role-based access to plugin features

### 3. Data Protection
- Escape output data
- Prevent SQL injection with prepared statements
- Secure file access for JSON boundaries

## Performance Optimization

### 1. Caching Strategy
- WordPress object cache for boundary data
- Browser caching for static assets
- Database query optimization

### 2. Asset Loading
- Conditional script/style loading
- Minified assets for production
- CDN support for external libraries

### 3. Data Optimization
- Lazy loading for large datasets
- Progressive enhancement
- Efficient JSON parsing

This system design provides a solid foundation for implementing the Dakoii Prov - Maps Plugin with scalability, security, and performance in mind.
