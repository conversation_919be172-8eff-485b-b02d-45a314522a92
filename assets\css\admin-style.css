/**
 * Dakoii Maps Admin Styles
 */

/* General <PERSON><PERSON> */
.dakoii-maps-admin {
    max-width: 1200px;
}

.dakoii-maps-admin .wrap {
    margin-right: 20px;
}

/* Header Styles */
.dakoii-maps-header {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.dakoii-maps-header h1 {
    margin: 0 0 10px 0;
    font-size: 24px;
    color: #23282d;
}

.dakoii-maps-header p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* Map List Table */
.dakoii-maps-table {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.dakoii-maps-table .wp-list-table {
    border: none;
    box-shadow: none;
}

.dakoii-maps-table .wp-list-table th,
.dakoii-maps-table .wp-list-table td {
    border-left: none;
    border-right: none;
}

.dakoii-maps-table .wp-list-table .column-shortcode {
    font-family: monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.dakoii-maps-table .wp-list-table .column-map_level {
    text-transform: capitalize;
}

.dakoii-maps-table .wp-list-table .column-map_level .level-badge {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.level-badge.provincial {
    background: #e3f2fd;
    color: #1976d2;
}

.level-badge.district {
    background: #f3e5f5;
    color: #7b1fa2;
}

.level-badge.llg {
    background: #e8f5e8;
    color: #388e3c;
}

/* Map Form Styles */
.dakoii-map-form {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.dakoii-form-section {
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.dakoii-form-section:last-child {
    border-bottom: none;
}

.dakoii-form-section h3 {
    margin: 0 0 15px 0;
    font-size: 18px;
    color: #23282d;
}

.dakoii-form-section p.description {
    margin: 0 0 15px 0;
    color: #666;
    font-style: italic;
}

.dakoii-form-row {
    margin-bottom: 20px;
}

.dakoii-form-row:last-child {
    margin-bottom: 0;
}

.dakoii-form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #23282d;
}

.dakoii-form-row input[type="text"],
.dakoii-form-row input[type="number"],
.dakoii-form-row input[type="color"],
.dakoii-form-row select,
.dakoii-form-row textarea {
    width: 100%;
    max-width: 400px;
}

.dakoii-form-row textarea {
    height: 80px;
    resize: vertical;
}

.dakoii-form-row .form-help {
    margin-top: 5px;
    font-size: 13px;
    color: #666;
}

/* Two Column Layout */
.dakoii-form-columns {
    display: flex;
    gap: 20px;
}

.dakoii-form-column {
    flex: 1;
}

.dakoii-form-column.narrow {
    flex: 0 0 200px;
}

/* Boundary Selection */
.boundary-selection {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    background: #f9f9f9;
}

.boundary-level-section {
    margin-bottom: 25px;
    padding: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.boundary-level-section:last-child {
    margin-bottom: 0;
}

.boundary-level-section h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: #23282d;
    font-weight: 600;
}

.boundary-level-section .description {
    margin-bottom: 15px;
    color: #666;
    font-size: 13px;
}

/* Parent Filters */
.parent-filter {
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 3px;
}

.parent-filter label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 13px;
}

.parent-filter select {
    width: 100%;
    max-width: 300px;
}

/* Boundary Dropdown Container */
.boundary-dropdown-container {
    margin-bottom: 15px;
}

.boundary-dropdown-container label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #23282d;
}

/* Boundary Selector (Multi-select dropdown) */
.boundary-selector {
    width: 100%;
    max-width: 500px;
    min-height: 200px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
    line-height: 1.4;
}

.boundary-selector option {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
}

.boundary-selector option:hover {
    background: #f0f0f1;
}

.boundary-selector option:checked {
    background: #0073aa;
    color: #fff;
}

.boundary-selector option:disabled {
    color: #999;
    font-style: italic;
    cursor: not-allowed;
}

/* Legacy boundary options (for backward compatibility) */
.boundary-options {
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 3px;
    margin-bottom: 10px;
}

.boundary-option {
    padding: 10px 12px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
}

.boundary-option:last-child {
    border-bottom: none;
}

.boundary-option:hover {
    background: #f0f0f1;
}

.boundary-option.selected {
    background: #e3f2fd;
    color: #1976d2;
}

.boundary-option input[type="checkbox"] {
    margin-right: 10px;
    margin-top: 0;
}

.boundary-option label {
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    width: 100%;
}

.boundary-option span {
    font-weight: 500;
}

.boundary-option small {
    margin-left: 8px;
    color: #666;
    font-size: 11px;
}

/* Boundary Actions */
.boundary-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.boundary-actions .button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
}

/* Selection Summary */
.selection-summary {
    margin-top: 20px;
    padding: 15px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.selected-count-wrapper {
    margin: 0;
    font-size: 14px;
}

.selected-count {
    color: #0073aa;
    font-weight: 600;
}

.boundary-type-label {
    color: #666;
    font-style: italic;
}

/* Loading and Error States */
.boundary-options .loading,
.boundary-options .error,
.boundary-options .no-boundaries {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

.boundary-options .error {
    color: #d63638;
}

.boundary-options .loading::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: dakoii-spin 1s linear infinite;
    vertical-align: middle;
}

/* Map Level Selection */
.map-level-options {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.map-level-option {
    flex: 1;
    border: 2px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
}

.map-level-option:hover {
    border-color: #0073aa;
}

.map-level-option.selected {
    border-color: #0073aa;
    background: #e3f2fd;
}

.map-level-option input[type="radio"] {
    display: none;
}

.map-level-option .level-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.map-level-option .level-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.map-level-option .level-description {
    font-size: 12px;
    color: #666;
}

/* Styling Options */
.styling-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.color-picker-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.color-picker-wrapper input[type="color"] {
    width: 40px;
    height: 30px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.color-picker-wrapper input[type="text"] {
    flex: 1;
    font-family: monospace;
}

/* Feature Toggles */
.feature-toggles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.feature-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
}

.feature-toggle input[type="checkbox"] {
    margin: 0;
}

.feature-toggle label {
    margin: 0;
    font-weight: normal;
    cursor: pointer;
}

/* Shortcode Display */
.shortcode-display {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-top: 20px;
}

.shortcode-display h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
}

.shortcode-code {
    font-family: monospace;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    word-break: break-all;
}

.shortcode-actions {
    display: flex;
    gap: 10px;
}

/* Loading States */
.dakoii-loading {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: dakoii-spin 1s linear infinite;
}

@keyframes dakoii-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dakoii-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Messages */
.dakoii-message {
    padding: 12px 16px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.dakoii-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.dakoii-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.dakoii-message.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* Buttons */
.dakoii-button {
    display: inline-block;
    padding: 8px 16px;
    border: 1px solid #0073aa;
    border-radius: 3px;
    background: #0073aa;
    color: #fff;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s;
}

.dakoii-button:hover {
    background: #005a87;
    border-color: #005a87;
    color: #fff;
}

.dakoii-button.secondary {
    background: #fff;
    color: #0073aa;
}

.dakoii-button.secondary:hover {
    background: #f0f0f1;
}

.dakoii-button.danger {
    background: #dc3545;
    border-color: #dc3545;
}

.dakoii-button.danger:hover {
    background: #c82333;
    border-color: #c82333;
}

/* Responsive */
@media (max-width: 768px) {
    .dakoii-form-columns {
        flex-direction: column;
    }
    
    .map-level-options {
        flex-direction: column;
    }
    
    .styling-options,
    .feature-toggles {
        grid-template-columns: 1fr;
    }
}
