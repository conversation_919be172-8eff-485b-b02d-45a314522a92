# Dakoii Prov - Maps Plugin User Guide

## Overview

The Dakoii Prov - Maps Plugin allows you to create and display interactive maps of Papua New Guinea's administrative boundaries on your WordPress website. You can create maps at three different levels: Provincial, District, and Local Level Government (LLG).

## Getting Started

### Installation

1. **Upload the Plugin**
   - Download the plugin zip file
   - Go to WordPress Admin → Plugins → Add New
   - Click "Upload Plugin" and select the zip file
   - Click "Install Now"

2. **Activate the Plugin**
   - After installation, click "Activate Plugin"
   - The plugin will create necessary database tables automatically

3. **Access the Plugin**
   - Go to WordPress Admin → Dakoii Maps
   - You'll see the main plugin dashboard

## Creating Your First Map

### Step 1: Access Map Creation

1. Navigate to **Dakoii Maps → Add New Map**
2. You'll see the map creation wizard

### Step 2: Basic Information

1. **Map Name**: Enter a descriptive name for your map
   - Example: "Western Province Districts"
   - Example: "National Capital District LLGs"

2. **Description**: Add an optional description
   - This helps you identify the map's purpose
   - Visible only in the admin area

### Step 3: Select Map Level

Choose the administrative level for your map:

1. **Provincial Level**
   - Shows all 22 provinces of Papua New Guinea
   - Users can click provinces to drill down to districts
   - Best for: National overviews, province selection

2. **District Level**
   - Shows districts within selected provinces
   - Users can click districts to drill down to LLGs
   - Best for: Regional focus, district comparisons

3. **LLG Level**
   - Shows Local Level Governments within selected districts
   - Most detailed administrative level
   - Best for: Local area focus, detailed boundaries

### Step 4: Geographic Scope

Select which areas to include in your map:

#### For Provincial Maps:
- **All Provinces**: Include all 22 provinces
- **Selected Provinces**: Choose specific provinces to display
  - Use checkboxes to select desired provinces
  - Hold Ctrl/Cmd to select multiple provinces

#### For District Maps:
- First select the parent province(s)
- Then choose specific districts within those provinces
- You can include districts from multiple provinces

#### For LLG Maps:
- First select the parent province(s)
- Then select the parent district(s)
- Finally choose specific LLGs to display

### Step 5: Display Configuration

#### Map Dimensions
- **Width**: Set map width (e.g., 100%, 800px)
- **Height**: Set map height (e.g., 500px, 400px)

#### Zoom Settings
- **Initial Zoom**: Starting zoom level (1-12)
- **Minimum Zoom**: Furthest zoom out allowed
- **Maximum Zoom**: Closest zoom in allowed

#### Map Center
- **Latitude**: Center point latitude (-6.314993 for PNG center)
- **Longitude**: Center point longitude (143.95555 for PNG center)

#### Styling Options
- **Boundary Color**: Color of boundary lines (#3388ff)
- **Boundary Width**: Thickness of boundary lines (1-5px)
- **Fill Color**: Interior color of areas (#3388ff)
- **Fill Opacity**: Transparency of fill (0.0-1.0)
- **Hover Color**: Color when mouse hovers over area
- **Hover Opacity**: Opacity during hover

#### Interactive Features
- **Enable Popups**: Show information when clicking areas
- **Enable Navigation**: Allow drilling down to child levels
- **Enable Search**: Add search functionality (future feature)
- **Show Legend**: Display map legend (future feature)

### Step 6: Preview and Generate

1. **Preview**: Click "Preview Map" to see how it will look
2. **Generate Shortcode**: Click "Create Map" to generate the shortcode
3. **Copy Shortcode**: Copy the generated shortcode for use in posts/pages

## Using Shortcodes

### Basic Usage

After creating a map, you'll receive a unique shortcode like:
```
[dakoii_map_abc123def]
```

### Embedding in Posts/Pages

1. **In the Classic Editor**:
   - Paste the shortcode directly into your post/page content
   - Example: `[dakoii_map_abc123def]`

2. **In the Block Editor (Gutenberg)**:
   - Add a "Shortcode" block
   - Paste your shortcode into the block
   - The map will display in the preview

3. **In Widgets**:
   - Use a "Text" or "Custom HTML" widget
   - Paste the shortcode into the widget content

### Shortcode Parameters (Advanced)

You can customize maps using shortcode parameters:

```
[dakoii_map_abc123def width="800px" height="600px" zoom="7"]
```

Available parameters:
- `width`: Override map width
- `height`: Override map height
- `zoom`: Override initial zoom level
- `center_lat`: Override center latitude
- `center_lng`: Override center longitude

## Managing Maps

### Viewing All Maps

1. Go to **Dakoii Maps → All Maps**
2. You'll see a table with all your created maps
3. Each row shows:
   - Map name and description
   - Map level (Provincial/District/LLG)
   - Shortcode
   - Creation date
   - Actions (Edit, Delete, Copy Shortcode)

### Editing Maps

1. Click "Edit" next to any map in the list
2. Modify any settings using the same wizard interface
3. Click "Update Map" to save changes
4. The shortcode remains the same, so existing embeds continue working

### Copying Shortcodes

1. Click "Copy Shortcode" next to any map
2. The shortcode is copied to your clipboard
3. Paste it wherever you want to display the map

### Deleting Maps

1. Click "Delete" next to any map
2. Confirm the deletion in the popup
3. **Warning**: This will break any existing shortcodes using this map

## Interactive Features

### Hierarchical Navigation

When viewing maps with navigation enabled:

1. **Provincial Maps**:
   - Click any province to zoom in and show its districts
   - Breadcrumb navigation appears at the top

2. **District Maps**:
   - Click any district to zoom in and show its LLGs
   - Use breadcrumbs to navigate back to parent levels

3. **Navigation Controls**:
   - "Reset View" button returns to original view
   - Breadcrumb links allow jumping to any parent level

### Information Popups

When popups are enabled:
- Click any boundary area to see information
- Popup shows:
  - Area name (Province/District/LLG name)
  - Geographic code
  - Additional details (if available)

### Map Controls

Standard map controls include:
- **Zoom In/Out**: + and - buttons
- **Pan**: Click and drag to move the map
- **Reset View**: Button to return to original view
- **Full Screen**: Expand map to full screen (future feature)

## Troubleshooting

### Map Not Displaying

1. **Check Shortcode**: Ensure the shortcode is correct
2. **Check Map Status**: Verify the map exists in admin area
3. **Browser Console**: Check for JavaScript errors
4. **Plugin Conflicts**: Temporarily deactivate other plugins

### Slow Loading

1. **Large Areas**: Maps with many boundaries load slower
2. **Server Resources**: Ensure adequate hosting resources
3. **Caching**: Enable caching plugins for better performance

### Styling Issues

1. **Theme Conflicts**: Your theme CSS might override map styles
2. **Custom CSS**: Add custom CSS to fix styling issues
3. **Container Size**: Ensure the map container has adequate space

### Common Error Messages

- **"Map not found"**: The shortcode references a deleted map
- **"Boundary data not available"**: JSON files are missing or corrupted
- **"Permission denied"**: User lacks necessary permissions

## Best Practices

### Map Design

1. **Purpose-Driven**: Create maps with specific purposes in mind
2. **Appropriate Level**: Choose the right administrative level for your content
3. **Clear Naming**: Use descriptive names for easy identification
4. **Consistent Styling**: Use similar colors and styles across related maps

### Performance

1. **Limit Scope**: Don't include unnecessary areas
2. **Reasonable Size**: Use appropriate dimensions for your layout
3. **Caching**: Enable WordPress caching for better performance
4. **Testing**: Test maps on different devices and browsers

### User Experience

1. **Context**: Provide context around your maps
2. **Instructions**: Tell users how to interact with the map
3. **Fallbacks**: Consider what happens if maps don't load
4. **Accessibility**: Ensure maps are accessible to all users

## Advanced Usage

### Custom Styling with CSS

Add custom CSS to your theme to modify map appearance:

```css
/* Customize map container */
.dakoii-map-container {
    border: 2px solid #ccc;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Customize popup styling */
.dakoii-popup {
    font-family: Arial, sans-serif;
    max-width: 200px;
}

.dakoii-popup h4 {
    margin: 0 0 10px 0;
    color: #333;
}
```

### Integration with Other Plugins

The plugin works well with:
- **Page Builders**: Elementor, Beaver Builder, etc.
- **Caching Plugins**: WP Rocket, W3 Total Cache, etc.
- **SEO Plugins**: Yoast SEO, RankMath, etc.

### Backup and Migration

1. **Database**: Include plugin tables in database backups
2. **Files**: Backup the entire plugin directory
3. **Export/Import**: Use WordPress export/import for content migration

## Support and Updates

### Getting Help

1. **Documentation**: Check this user guide first
2. **WordPress Forums**: Search for similar issues
3. **Plugin Support**: Contact plugin developers
4. **Community**: Join Papua New Guinea WordPress communities

### Keeping Updated

1. **Plugin Updates**: Install updates when available
2. **WordPress Updates**: Keep WordPress core updated
3. **Boundary Data**: Updates may include new boundary data
4. **Feature Requests**: Suggest new features to developers

This user guide provides comprehensive instructions for using the Dakoii Prov - Maps Plugin effectively. Refer to specific sections as needed for your mapping requirements.
