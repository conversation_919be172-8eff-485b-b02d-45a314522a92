<?php
/**
 * Plugin Name: Da<PERSON><PERSON> Prov - Maps Plugin
 * Plugin URI: https://your-website.com/dakoii-prov-maps
 * Description: Interactive maps for Papua New Guinea administrative boundaries at Provincial, District, and LLG levels with hierarchical navigation and shortcode support.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://your-website.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: dakoii-maps
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('DAKOII_MAPS_VERSION', '1.0.0');
define('DAKOII_MAPS_PLUGIN_FILE', __FILE__);
define('DAKOII_MAPS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DAKOII_MAPS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('DAKOII_MAPS_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Plugin Class
 */
class DakoiiProvMaps {
    
    /**
     * Single instance of the plugin
     */
    private static $instance = null;
    
    /**
     * Plugin version
     */
    public $version = DAKOII_MAPS_VERSION;
    
    /**
     * Database version
     */
    public $db_version = '1.0.0';
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(DAKOII_MAPS_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(DAKOII_MAPS_PLUGIN_FILE, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Admin hooks
        if (is_admin()) {
            add_action('admin_init', array($this, 'admin_init'));
            add_action('admin_menu', array($this, 'admin_menu'));
            add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
        }
        
        // Frontend hooks
        add_action('wp_enqueue_scripts', array($this, 'frontend_enqueue_scripts'));
        
        // AJAX hooks
        add_action('wp_ajax_dakoii_get_boundaries', array($this, 'ajax_get_boundaries'));
        add_action('wp_ajax_nopriv_dakoii_get_boundaries', array($this, 'ajax_get_boundaries'));
        add_action('wp_ajax_dakoii_get_map_data', array($this, 'ajax_get_map_data'));
        add_action('wp_ajax_nopriv_dakoii_get_map_data', array($this, 'ajax_get_map_data'));
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        $this->load_dependencies();
        $this->init_components();
        
        // Check if database needs update
        $this->maybe_update_database();
    }
    
    /**
     * Load plugin dependencies
     */
    private function load_dependencies() {
        // Core classes
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-map-manager.php';
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-boundary-loader.php';
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-shortcode-handler.php';
        require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-cache-manager.php';
        
        // Admin classes
        if (is_admin()) {
            require_once DAKOII_MAPS_PLUGIN_DIR . 'includes/class-admin-interface.php';
        }
    }
    
    /**
     * Initialize components
     */
    private function init_components() {
        // Initialize core components
        new DakoiiMapManager();
        new DakoniiBoundaryLoader();
        new DakoiiShortcodeHandler();
        new DakoiiCacheManager();
        
        // Initialize admin interface
        if (is_admin()) {
            new DakoiiAdminInterface();
        }
    }
    
    /**
     * Load text domain for translations
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'dakoii-maps',
            false,
            dirname(DAKOII_MAPS_PLUGIN_BASENAME) . '/languages'
        );
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set activation flag
        update_option('dakoii_maps_activated', true);
        
        // Log activation
        error_log('Dakoii Maps Plugin activated successfully');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook('dakoii_maps_cleanup');
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Log deactivation
        error_log('Dakoii Maps Plugin deactivated');
    }
    
    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Maps table
        $maps_table = $wpdb->prefix . 'dakoii_maps';
        $maps_sql = "CREATE TABLE $maps_table (
            id int(11) unsigned NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            map_level enum('provincial','district','llg') NOT NULL,
            config longtext NOT NULL,
            shortcode varchar(50) NOT NULL,
            status enum('active','inactive','draft') DEFAULT 'active',
            created_by int(11) unsigned,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY shortcode (shortcode),
            KEY map_level (map_level),
            KEY status (status),
            KEY created_by (created_by),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        // Map boundaries table
        $boundaries_table = $wpdb->prefix . 'dakoii_map_boundaries';
        $boundaries_sql = "CREATE TABLE $boundaries_table (
            id int(11) unsigned NOT NULL AUTO_INCREMENT,
            map_id int(11) unsigned NOT NULL,
            boundary_type enum('province','district','llg') NOT NULL,
            boundary_id varchar(50) NOT NULL,
            boundary_name varchar(255) NOT NULL,
            geocode varchar(50),
            display_order int(11) DEFAULT 0,
            is_active boolean DEFAULT TRUE,
            PRIMARY KEY (id),
            KEY map_id (map_id),
            KEY boundary_type (boundary_type),
            KEY geocode (geocode),
            KEY display_order (display_order),
            FOREIGN KEY (map_id) REFERENCES $maps_table(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($maps_sql);
        dbDelta($boundaries_sql);
        
        // Update database version
        update_option('dakoii_maps_db_version', $this->db_version);
    }
    
    /**
     * Set default options
     */
    private function set_default_options() {
        $default_settings = array(
            'cache_enabled' => true,
            'cache_expiry' => 3600,
            'default_map_width' => '100%',
            'default_map_height' => '500px',
            'default_zoom_level' => 6,
            'enable_debug' => false
        );
        
        add_option('dakoii_maps_settings', $default_settings);
        add_option('dakoii_maps_version', $this->version);
    }
    
    /**
     * Check if database needs update
     */
    private function maybe_update_database() {
        $current_db_version = get_option('dakoii_maps_db_version', '0.0.0');
        
        if (version_compare($current_db_version, $this->db_version, '<')) {
            $this->create_tables();
        }
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Register settings
        register_setting('dakoii_maps_settings', 'dakoii_maps_settings');
        
        // Add settings sections and fields
        $this->add_settings_sections();
    }
    
    /**
     * Add admin menu
     */
    public function admin_menu() {
        // Main menu page
        add_menu_page(
            __('Dakoii Maps', 'dakoii-maps'),
            __('Dakoii Maps', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps',
            array($this, 'admin_page_maps'),
            'dashicons-location-alt',
            30
        );
        
        // Submenu pages
        add_submenu_page(
            'dakoii-maps',
            __('All Maps', 'dakoii-maps'),
            __('All Maps', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps',
            array($this, 'admin_page_maps')
        );
        
        add_submenu_page(
            'dakoii-maps',
            __('Add New Map', 'dakoii-maps'),
            __('Add New Map', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps-add',
            array($this, 'admin_page_add_map')
        );
        
        add_submenu_page(
            'dakoii-maps',
            __('Settings', 'dakoii-maps'),
            __('Settings', 'dakoii-maps'),
            'manage_options',
            'dakoii-maps-settings',
            array($this, 'admin_page_settings')
        );
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts($hook) {
        // Only load on plugin pages
        if (strpos($hook, 'dakoii-maps') === false) {
            return;
        }
        
        // Admin CSS
        wp_enqueue_style(
            'dakoii-maps-admin',
            DAKOII_MAPS_PLUGIN_URL . 'assets/css/admin-style.css',
            array(),
            $this->version
        );
        
        // Admin JS
        wp_enqueue_script(
            'dakoii-maps-admin',
            DAKOII_MAPS_PLUGIN_URL . 'assets/js/admin-script.js',
            array('jquery'),
            $this->version,
            true
        );
        
        // Localize script
        wp_localize_script('dakoii-maps-admin', 'dakoiiMapsAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dakoii_maps_admin_nonce'),
            'strings' => array(
                'confirmDelete' => __('Are you sure you want to delete this map?', 'dakoii-maps'),
                'loading' => __('Loading...', 'dakoii-maps'),
                'error' => __('An error occurred. Please try again.', 'dakoii-maps')
            )
        ));
    }
    
    /**
     * Enqueue frontend scripts and styles
     */
    public function frontend_enqueue_scripts() {
        // Only load if shortcode is present
        if (!$this->has_shortcode_in_content()) {
            return;
        }
        
        // Leaflet CSS
        wp_enqueue_style(
            'leaflet',
            DAKOII_MAPS_PLUGIN_URL . 'assets/js/leaflet/leaflet.css',
            array(),
            '1.9.4'
        );
        
        // Frontend CSS
        wp_enqueue_style(
            'dakoii-maps-frontend',
            DAKOII_MAPS_PLUGIN_URL . 'assets/css/frontend-style.css',
            array('leaflet'),
            $this->version
        );
        
        // Leaflet JS
        wp_enqueue_script(
            'leaflet',
            DAKOII_MAPS_PLUGIN_URL . 'assets/js/leaflet/leaflet.js',
            array(),
            '1.9.4',
            true
        );
        
        // Frontend JS
        wp_enqueue_script(
            'dakoii-maps-frontend',
            DAKOII_MAPS_PLUGIN_URL . 'assets/js/frontend-script.js',
            array('jquery', 'leaflet'),
            $this->version,
            true
        );
        
        // Localize script
        wp_localize_script('dakoii-maps-frontend', 'dakoiiMaps', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dakoii_maps_nonce'),
            'pluginUrl' => DAKOII_MAPS_PLUGIN_URL
        ));
    }
    
    /**
     * Check if shortcode is present in content
     */
    private function has_shortcode_in_content() {
        global $post;
        
        if (is_a($post, 'WP_Post') && has_shortcode($post->post_content, 'dakoii_map')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Add settings sections
     */
    private function add_settings_sections() {
        // General settings section
        add_settings_section(
            'dakoii_maps_general',
            __('General Settings', 'dakoii-maps'),
            array($this, 'settings_section_callback'),
            'dakoii_maps_settings'
        );
    }
    
    /**
     * Settings section callback
     */
    public function settings_section_callback() {
        echo '<p>' . __('Configure general settings for the Dakoii Maps plugin.', 'dakoii-maps') . '</p>';
    }
    
    /**
     * Admin page methods (delegated to admin interface class)
     */
    public function admin_page_maps() {
        if (class_exists('DakoiiAdminInterface')) {
            $admin = new DakoiiAdminInterface();
            $admin->admin_page_maps();
        } else {
            echo '<div class="wrap"><h1>Admin interface not loaded</h1></div>';
        }
    }

    public function admin_page_add_map() {
        if (class_exists('DakoiiAdminInterface')) {
            $admin = new DakoiiAdminInterface();
            $admin->admin_page_add_map();
        } else {
            echo '<div class="wrap"><h1>Admin interface not loaded</h1></div>';
        }
    }

    public function admin_page_settings() {
        if (class_exists('DakoiiAdminInterface')) {
            $admin = new DakoiiAdminInterface();
            $admin->admin_page_settings();
        } else {
            echo '<div class="wrap"><h1>Admin interface not loaded</h1></div>';
        }
    }
    
    /**
     * AJAX handlers (placeholders)
     */
    public function ajax_get_boundaries() {
        wp_die('AJAX handler will be implemented');
    }
    
    public function ajax_get_map_data() {
        wp_die('AJAX handler will be implemented');
    }
}

/**
 * Initialize the plugin
 */
function dakoii_prov_maps() {
    return DakoiiProvMaps::get_instance();
}

// Start the plugin
dakoii_prov_maps();
