<?php
/**
 * Admin template: Map List
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Handle messages
$message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
$error = isset($_GET['error']) ? sanitize_text_field($_GET['error']) : '';
?>

<div class="wrap dakoii-maps-admin">
    <div class="dakoii-maps-header">
        <h1>
            <?php _e('Dakoii Provincial Map Boundries', 'dakoii-maps'); ?>
            <a href="<?php echo admin_url('admin.php?page=dakoii-maps-add'); ?>" class="page-title-action">
                <?php _e('Add New Map', 'dakoii-maps'); ?>
            </a>
        </h1>
        <p><?php _e('Manage your interactive Papua New Guinea administrative boundary maps.', 'dakoii-maps'); ?></p>
    </div>

    <?php if ($message): ?>
        <div class="notice notice-success is-dismissible">
            <p>
                <?php
                switch ($message) {
                    case 'saved':
                        _e('Map saved successfully.', 'dakoii-maps');
                        break;
                    case 'deleted':
                        _e('Map deleted successfully.', 'dakoii-maps');
                        break;
                    case 'bulk_deleted':
                        $count = isset($_GET['deleted_count']) ? intval($_GET['deleted_count']) : 0;
                        printf(_n('%d map deleted successfully.', '%d maps deleted successfully.', $count, 'dakoii-maps'), $count);
                        break;
                    default:
                        echo esc_html($message);
                }
                ?>
            </p>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="notice notice-error is-dismissible">
            <p>
                <?php
                switch ($error) {
                    case 'save_failed':
                        _e('Failed to save map. Please try again.', 'dakoii-maps');
                        break;
                    case 'delete_failed':
                        _e('Failed to delete map. Please try again.', 'dakoii-maps');
                        break;
                    default:
                        echo esc_html($error);
                }
                ?>
            </p>
        </div>
    <?php endif; ?>

    <div class="dakoii-maps-table">
        <?php if (empty($maps)): ?>
            <div class="no-maps-message" style="padding: 40px; text-align: center; background: #fff; border: 1px solid #ccd0d4; border-radius: 4px;">
                <h3><?php _e('No maps found', 'dakoii-maps'); ?></h3>
                <p><?php _e('You haven\'t created any maps yet. Get started by creating your first map!', 'dakoii-maps'); ?></p>
                <a href="<?php echo admin_url('admin.php?page=dakoii-maps-add'); ?>" class="button button-primary">
                    <?php _e('Create Your First Map', 'dakoii-maps'); ?>
                </a>
            </div>
        <?php else: ?>
            <form method="post" action="">
                <?php wp_nonce_field('bulk-maps'); ?>
                
                <div class="tablenav top">
                    <div class="alignleft actions bulkactions">
                        <label for="bulk-action-selector-top" class="screen-reader-text">
                            <?php _e('Select bulk action', 'dakoii-maps'); ?>
                        </label>
                        <select name="action" id="bulk-action-selector-top">
                            <option value="-1"><?php _e('Bulk Actions', 'dakoii-maps'); ?></option>
                            <option value="delete"><?php _e('Delete', 'dakoii-maps'); ?></option>
                        </select>
                        <input type="submit" class="button action" value="<?php _e('Apply', 'dakoii-maps'); ?>">
                    </div>
                    
                    <div class="tablenav-pages">
                        <?php
                        $pagination_args = array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => ceil($total_maps / $per_page),
                            'current' => $current_page
                        );
                        echo paginate_links($pagination_args);
                        ?>
                    </div>
                </div>

                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <td class="manage-column column-cb check-column">
                                <label class="screen-reader-text" for="cb-select-all-1">
                                    <?php _e('Select All', 'dakoii-maps'); ?>
                                </label>
                                <input id="cb-select-all-1" type="checkbox">
                            </td>
                            <th class="manage-column column-name column-primary">
                                <?php _e('Map Name', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-map_level">
                                <?php _e('Level', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-shortcode">
                                <?php _e('Shortcode', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-status">
                                <?php _e('Status', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-date">
                                <?php _e('Created', 'dakoii-maps'); ?>
                            </th>
                        </tr>
                    </thead>
                    
                    <tbody>
                        <?php foreach ($maps as $map): ?>
                            <tr>
                                <th class="check-column">
                                    <label class="screen-reader-text" for="cb-select-<?php echo $map->id; ?>">
                                        <?php printf(__('Select %s', 'dakoii-maps'), esc_html($map->name)); ?>
                                    </label>
                                    <input id="cb-select-<?php echo $map->id; ?>" type="checkbox" name="map_ids[]" value="<?php echo $map->id; ?>">
                                </th>
                                
                                <td class="column-name column-primary">
                                    <strong>
                                        <a href="<?php echo admin_url('admin.php?page=dakoii-maps-edit&map_id=' . $map->id); ?>">
                                            <?php echo esc_html($map->name); ?>
                                        </a>
                                    </strong>
                                    
                                    <?php if (!empty($map->description)): ?>
                                        <p class="description"><?php echo esc_html(wp_trim_words($map->description, 15)); ?></p>
                                    <?php endif; ?>
                                    
                                    <div class="row-actions">
                                        <span class="edit">
                                            <a href="<?php echo admin_url('admin.php?page=dakoii-maps-edit&map_id=' . $map->id); ?>">
                                                <?php _e('Edit', 'dakoii-maps'); ?>
                                            </a> |
                                        </span>
                                        <span class="copy">
                                            <a href="#" class="copy-shortcode" data-shortcode="[<?php echo esc_attr($map->shortcode); ?>]">
                                                <?php _e('Copy Shortcode', 'dakoii-maps'); ?>
                                            </a> |
                                        </span>
                                        <span class="delete">
                                            <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=dakoii-maps&action=delete&map_id=' . $map->id), 'delete_map_' . $map->id); ?>" 
                                               class="delete-map submitdelete">
                                                <?php _e('Delete', 'dakoii-maps'); ?>
                                            </a>
                                        </span>
                                    </div>
                                    
                                    <button type="button" class="toggle-row">
                                        <span class="screen-reader-text"><?php _e('Show more details', 'dakoii-maps'); ?></span>
                                    </button>
                                </td>
                                
                                <td class="column-map_level" data-colname="<?php _e('Level', 'dakoii-maps'); ?>">
                                    <span class="level-badge <?php echo esc_attr($map->map_level); ?>">
                                        <?php echo esc_html(ucfirst($map->map_level)); ?>
                                    </span>
                                </td>
                                
                                <td class="column-shortcode" data-colname="<?php _e('Shortcode', 'dakoii-maps'); ?>">
                                    <code class="shortcode-display">[<?php echo esc_html($map->shortcode); ?>]</code>
                                    <button type="button" class="button button-small copy-shortcode" 
                                            data-shortcode="[<?php echo esc_attr($map->shortcode); ?>]">
                                        <?php _e('Copy', 'dakoii-maps'); ?>
                                    </button>
                                </td>
                                
                                <td class="column-status" data-colname="<?php _e('Status', 'dakoii-maps'); ?>">
                                    <?php
                                    $status_labels = array(
                                        'active' => __('Active', 'dakoii-maps'),
                                        'inactive' => __('Inactive', 'dakoii-maps'),
                                        'draft' => __('Draft', 'dakoii-maps')
                                    );
                                    echo esc_html($status_labels[$map->status] ?? $map->status);
                                    ?>
                                </td>
                                
                                <td class="column-date" data-colname="<?php _e('Created', 'dakoii-maps'); ?>">
                                    <?php
                                    $created_date = new DateTime($map->created_at);
                                    echo $created_date->format('M j, Y');
                                    ?>
                                    <br>
                                    <small><?php echo $created_date->format('g:i a'); ?></small>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    
                    <tfoot>
                        <tr>
                            <td class="manage-column column-cb check-column">
                                <label class="screen-reader-text" for="cb-select-all-2">
                                    <?php _e('Select All', 'dakoii-maps'); ?>
                                </label>
                                <input id="cb-select-all-2" type="checkbox">
                            </td>
                            <th class="manage-column column-name column-primary">
                                <?php _e('Map Name', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-map_level">
                                <?php _e('Level', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-shortcode">
                                <?php _e('Shortcode', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-status">
                                <?php _e('Status', 'dakoii-maps'); ?>
                            </th>
                            <th class="manage-column column-date">
                                <?php _e('Created', 'dakoii-maps'); ?>
                            </th>
                        </tr>
                    </tfoot>
                </table>

                <div class="tablenav bottom">
                    <div class="alignleft actions bulkactions">
                        <label for="bulk-action-selector-bottom" class="screen-reader-text">
                            <?php _e('Select bulk action', 'dakoii-maps'); ?>
                        </label>
                        <select name="action2" id="bulk-action-selector-bottom">
                            <option value="-1"><?php _e('Bulk Actions', 'dakoii-maps'); ?></option>
                            <option value="delete"><?php _e('Delete', 'dakoii-maps'); ?></option>
                        </select>
                        <input type="submit" class="button action" value="<?php _e('Apply', 'dakoii-maps'); ?>">
                    </div>
                    
                    <div class="tablenav-pages">
                        <?php echo paginate_links($pagination_args); ?>
                    </div>
                </div>
            </form>
        <?php endif; ?>
    </div>

    <div class="dakoii-maps-footer" style="margin-top: 20px; padding: 20px; background: #fff; border: 1px solid #ccd0d4; border-radius: 4px;">
        <h3><?php _e('Quick Stats', 'dakoii-maps'); ?></h3>
        <p>
            <?php printf(__('Total Maps: %d', 'dakoii-maps'), $total_maps); ?> |
            <a href="<?php echo admin_url('admin.php?page=dakoii-maps-settings'); ?>">
                <?php _e('Settings', 'dakoii-maps'); ?>
            </a> |
            <a href="#" target="_blank"><?php _e('Documentation', 'dakoii-maps'); ?></a>
        </p>
    </div>
</div>
